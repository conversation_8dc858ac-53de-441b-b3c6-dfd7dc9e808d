"""
MCP文件操作服务端类
基于FastMCP实现的文件操作服务，提供完整的文件系统操作功能
"""
import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
import json
import threading
import time
from mcp.server.fastmcp import FastMCP


class MCPFileServer:
    """MCP文件操作服务端类"""

    def __init__(self, name: str = "File Operation Server", host: str = "localhost", port: int = 3001):
        """
        初始化MCP文件服务器

        Args:
            name: 服务器名称
            host: 服务器主机地址
            port: 服务器端口
        """
        self.name = name
        self.host = host
        self.port = port
        self.mcp = FastMCP(name, host=host, port=port)
        self.server_thread = None
        self.is_running = False
        self.workspace_root = Path.cwd()

        # 注册所有工具
        self._register_tools()

    def _register_tools(self):
        """注册所有文件操作工具"""

        @self.mcp.tool(description="读取文件内容")
        def read_file(filepath: str) -> Dict[str, Any]:
            """读取并返回文件内容"""
            try:
                file_path = Path(filepath)
                if not file_path.is_absolute():
                    file_path = self.workspace_root / file_path

                if not file_path.exists():
                    return {"success": False, "error": f"文件不存在: {filepath}"}

                if not file_path.is_file():
                    return {"success": False, "error": f"路径不是文件: {filepath}"}

                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                return {
                    "success": True,
                    "content": content,
                    "filepath": str(file_path),
                    "size": len(content)
                }
            except Exception as e:
                return {"success": False, "error": f"读取文件失败: {str(e)}"}

        @self.mcp.tool(description="写入文件内容")
        def write_file(filepath: str, content: str, mode: str = "w") -> Dict[str, Any]:
            """写入内容到文件"""
            try:
                file_path = Path(filepath)
                if not file_path.is_absolute():
                    file_path = self.workspace_root / file_path

                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)

                with open(file_path, mode, encoding="utf-8") as f:
                    f.write(content)

                return {
                    "success": True,
                    "filepath": str(file_path),
                    "size": len(content),
                    "message": f"文件写入成功: {filepath}"
                }
            except Exception as e:
                return {"success": False, "error": f"写入文件失败: {str(e)}"}

        @self.mcp.tool(description="列出目录内容")
        def list_directory(dirpath: str = ".") -> Dict[str, Any]:
            """列出给定目录中的所有文件和目录"""
            try:
                dir_path = Path(dirpath)
                if not dir_path.is_absolute():
                    dir_path = self.workspace_root / dir_path

                if not dir_path.exists():
                    return {"success": False, "error": f"目录不存在: {dirpath}"}

                if not dir_path.is_dir():
                    return {"success": False, "error": f"路径不是目录: {dirpath}"}

                items = []
                for item in dir_path.iterdir():
                    items.append({
                        "name": item.name,
                        "path": str(item),
                        "type": "directory" if item.is_dir() else "file",
                        "size": item.stat().st_size if item.is_file() else None
                    })

                return {
                    "success": True,
                    "dirpath": str(dir_path),
                    "items": items,
                    "count": len(items)
                }
            except Exception as e:
                return {"success": False, "error": f"列出目录失败: {str(e)}"}

        @self.mcp.tool(description="创建目录")
        def create_directory(dirpath: str) -> Dict[str, Any]:
            """创建目录"""
            try:
                dir_path = Path(dirpath)
                if not dir_path.is_absolute():
                    dir_path = self.workspace_root / dir_path

                dir_path.mkdir(parents=True, exist_ok=True)

                return {
                    "success": True,
                    "dirpath": str(dir_path),
                    "message": f"目录创建成功: {dirpath}"
                }
            except Exception as e:
                return {"success": False, "error": f"创建目录失败: {str(e)}"}

        @self.mcp.tool(description="删除文件或目录")
        def delete_path(filepath: str) -> Dict[str, Any]:
            """删除文件或目录"""
            try:
                file_path = Path(filepath)
                if not file_path.is_absolute():
                    file_path = self.workspace_root / file_path

                if not file_path.exists():
                    return {"success": False, "error": f"路径不存在: {filepath}"}

                if file_path.is_file():
                    file_path.unlink()
                    message = f"文件删除成功: {filepath}"
                elif file_path.is_dir():
                    shutil.rmtree(file_path)
                    message = f"目录删除成功: {filepath}"
                else:
                    return {"success": False, "error": f"未知路径类型: {filepath}"}

                return {
                    "success": True,
                    "filepath": str(file_path),
                    "message": message
                }
            except Exception as e:
                return {"success": False, "error": f"删除路径失败: {str(e)}"}

        @self.mcp.tool(description="复制文件或目录")
        def copy_path(src: str, dst: str) -> Dict[str, Any]:
            """复制文件或目录"""
            try:
                src_path = Path(src)
                dst_path = Path(dst)

                if not src_path.is_absolute():
                    src_path = self.workspace_root / src_path
                if not dst_path.is_absolute():
                    dst_path = self.workspace_root / dst_path

                if not src_path.exists():
                    return {"success": False, "error": f"源路径不存在: {src}"}

                # 确保目标目录存在
                dst_path.parent.mkdir(parents=True, exist_ok=True)

                if src_path.is_file():
                    shutil.copy2(src_path, dst_path)
                    message = f"文件复制成功: {src} -> {dst}"
                elif src_path.is_dir():
                    shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
                    message = f"目录复制成功: {src} -> {dst}"
                else:
                    return {"success": False, "error": f"未知源路径类型: {src}"}

                return {
                    "success": True,
                    "src": str(src_path),
                    "dst": str(dst_path),
                    "message": message
                }
            except Exception as e:
                return {"success": False, "error": f"复制路径失败: {str(e)}"}

        @self.mcp.tool(description="移动/重命名文件或目录")
        def move_path(src: str, dst: str) -> Dict[str, Any]:
            """移动/重命名文件或目录"""
            try:
                src_path = Path(src)
                dst_path = Path(dst)

                if not src_path.is_absolute():
                    src_path = self.workspace_root / src_path
                if not dst_path.is_absolute():
                    dst_path = self.workspace_root / dst_path

                if not src_path.exists():
                    return {"success": False, "error": f"源路径不存在: {src}"}

                # 确保目标目录存在
                dst_path.parent.mkdir(parents=True, exist_ok=True)

                shutil.move(str(src_path), str(dst_path))

                return {
                    "success": True,
                    "src": str(src_path),
                    "dst": str(dst_path),
                    "message": f"移动成功: {src} -> {dst}"
                }
            except Exception as e:
                return {"success": False, "error": f"移动路径失败: {str(e)}"}

        @self.mcp.tool(description="获取文件或目录信息")
        def get_path_info(filepath: str) -> Dict[str, Any]:
            """获取文件或目录的详细信息"""
            try:
                file_path = Path(filepath)
                if not file_path.is_absolute():
                    file_path = self.workspace_root / file_path

                if not file_path.exists():
                    return {"success": False, "error": f"路径不存在: {filepath}"}

                stat = file_path.stat()

                return {
                    "success": True,
                    "path": str(file_path),
                    "name": file_path.name,
                    "type": "directory" if file_path.is_dir() else "file",
                    "size": stat.st_size,
                    "created": stat.st_ctime,
                    "modified": stat.st_mtime,
                    "accessed": stat.st_atime,
                    "exists": True
                }
            except Exception as e:
                return {"success": False, "error": f"获取路径信息失败: {str(e)}"}

        @self.mcp.tool(description="获取当前工作目录")
        def get_current_directory() -> Dict[str, Any]:
            """返回当前工作目录"""
            try:
                return {
                    "success": True,
                    "current_directory": str(self.workspace_root),
                    "absolute_path": str(self.workspace_root.absolute())
                }
            except Exception as e:
                return {"success": False, "error": f"获取当前目录失败: {str(e)}"}

    def start_server(self):
        """启动MCP服务器"""
        if self.is_running:
            return {"success": False, "error": "服务器已在运行"}

        try:
            def run_server():
                self.mcp.run()

            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()
            self.is_running = True

            # 等待服务器启动
            time.sleep(1)

            return {
                "success": True,
                "message": f"MCP文件服务器已启动",
                "host": self.host,
                "port": self.port
            }
        except Exception as e:
            return {"success": False, "error": f"启动服务器失败: {str(e)}"}

    def stop_server(self):
        """停止MCP服务器"""
        if not self.is_running:
            return {"success": False, "error": "服务器未运行"}

        try:
            self.is_running = False
            return {
                "success": True,
                "message": "MCP文件服务器已停止"
            }
        except Exception as e:
            return {"success": False, "error": f"停止服务器失败: {str(e)}"}

    def get_server_status(self):
        """获取服务器状态"""
        return {
            "name": self.name,
            "host": self.host,
            "port": self.port,
            "is_running": self.is_running,
            "workspace_root": str(self.workspace_root)
        }


# 创建全局实例
mcp_file_server = MCPFileServer()