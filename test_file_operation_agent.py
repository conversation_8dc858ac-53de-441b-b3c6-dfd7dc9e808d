#!/usr/bin/env python3
"""
文件操作智能体测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_file_operation_agent():
    """测试文件操作智能体"""
    try:
        print("=" * 60)
        print("文件操作智能体测试")
        print("=" * 60)

        # 导入文件操作智能体
        from agents.file_operation_agent import file_operation_agent

        print(f"智能体名称: {file_operation_agent.task_name}")
        print(f"总步骤数: {len(file_operation_agent.steps)}")
        print("步骤列表:")
        for i, step in enumerate(file_operation_agent.steps, 1):
            print(f"  {i}. {step}")

        # 获取当前步骤信息
        step_info = file_operation_agent.get_current_step_info()
        print(f"\n当前步骤: {step_info['step_number']}/{step_info['total_steps']} - {step_info['step_name']}")

        print("\n✓ 文件操作智能体导入成功")
        return True

    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_file_server():
    """测试MCP文件服务器"""
    try:
        print("\n" + "=" * 60)
        print("MCP文件服务器测试")
        print("=" * 60)

        # 导入MCP文件服务器
        from mcp.mcp_file_server import mcp_file_server

        print(f"服务器名称: {mcp_file_server.name}")
        print(f"服务器地址: {mcp_file_server.host}:{mcp_file_server.port}")
        print(f"工作目录: {mcp_file_server.workspace_root}")

        # 获取服务器状态
        status = mcp_file_server.get_server_status()
        print(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")

        print("\n✓ MCP文件服务器导入成功")
        return True

    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    try:
        print("\n" + "=" * 60)
        print("GUI集成测试")
        print("=" * 60)

        # 导入GUI主窗口
        from gui.main_window import MainWindow

        print("✓ GUI主窗口类导入成功")

        # 检查是否包含文件操作智能体的导入
        import gui.main_window as main_window_module
        if hasattr(main_window_module, 'file_operation_agent'):
            print("✓ 文件操作智能体已集成到GUI")
        else:
            print("✗ 文件操作智能体未集成到GUI")
            return False

        return True

    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试文件操作智能体功能...")

    results = []

    # 测试文件操作智能体
    results.append(test_file_operation_agent())

    # 测试MCP文件服务器
    results.append(test_mcp_file_server())

    # 测试GUI集成
    results.append(test_gui_integration())

    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)

    passed = sum(results)
    total = len(results)

    print(f"通过测试: {passed}/{total}")

    if passed == total:
        print("🎉 所有测试通过！文件操作智能体功能正常")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)