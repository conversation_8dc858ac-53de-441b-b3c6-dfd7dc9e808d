# 智能体全流程展示界面 - PySide6版本启动说明

## 快速启动

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动PySide6桌面应用**
```bash
python main_gui.py
```

3. **使用界面**
桌面应用窗口将自动打开，无需浏览器

## 项目已完成功能

### ✅ 已实现的功能

1. **项目结构完整**
   - 配置管理系统 (configs/config.yaml)
   - 日志系统 (logs/ 目录，按天滚动，保留365天)
   - PySide6桌面界面 (gui/main_window.py)
   - 智能体模块 (agents/ 目录)
   - 增强显示管理 (utils/display_manager.py)

2. **配置区功能**
   - 大模型配置界面 (Base URL: http://***********:12434/v1, API Key: EMPTY, Model: Qwen3-Coder)
   - 配置保存到YAML文件并实时同步
   - 配置加载功能

3. **功能区按键**
   - **任务1**: 大模型调用示例 - 测试连接按键，询问什么是MCP
   - **任务2**: 冒泡排序智能体 - 7个步骤按键
     - 步骤1: 制作任务计划
     - 步骤2: 咨询大模型-算法实现
     - 步骤3: 分析大模型返回
     - 步骤4: 咨询大模型-代码结构
     - 步骤5: 生成代码文件
     - 步骤6: 代码验证和测试
     - 步骤7: 任务完成总结
   - **任务3**: HTML文件制作 - 7个步骤按键
     - 步骤1: 分析HTML需求
     - 步骤2: 咨询大模型-HTML结构
     - 步骤3: 咨询大模型-样式设计
     - 步骤4: 生成HTML文件
     - 步骤5: 生成CSS样式文件
     - 步骤6: 验证文件完整性
     - 步骤7: 任务完成总结
   - **任务4**: 预留功能按键

4. **展示区功能**
   - 实时显示任务执行过程
   - 显示执行的Python代码和作用
   - 显示大模型交互详情
   - 显示工具调用结果和执行过程
   - 自动滚动和清空功能

5. **智能体工作流程**
   - 详细的步骤拆解
   - 大模型咨询记录
   - 工具调用日志
   - 任务执行状态跟踪

6. **日志系统**
   - 按天滚动日志
   - 详细的操作记录
   - 任务执行追踪
   - 错误日志记录

## 使用流程

### 第一次使用

1. **配置大模型**
   - 在配置区填入大模型的API信息
   - 点击"保存配置"
   - 点击"测试大模型连接"验证配置

2. **体验冒泡排序任务**
   - 点击"重置任务"
   - 依次点击步骤1-7按键
   - 观察展示区的详细执行过程
   - 查看生成的代码文件 (output/bubble_sort.py)

3. **体验HTML制作任务**
   - 点击"重置任务"
   - 依次点击步骤1-7按键
   - 观察展示区的详细执行过程
   - 查看生成的HTML文件 (output/resume.html, output/styles.css)

### 核心特色

- **步骤化控制**: 每个智能体任务都被拆分为独立的步骤，可以单独执行
- **实时展示**: 每个步骤的执行过程、大模型交互、工具调用都会实时显示
- **详细日志**: 所有操作都会记录在日志文件中，便于追踪和调试
- **配置管理**: 界面化的配置管理，修改后自动保存到文件

## 文件输出

执行任务后，生成的文件会保存在 `workspace_mcp/` 目录中：
- 冒泡排序任务: `workspace_mcp/bubble_sort.py`
- HTML制作任务: `workspace_mcp/resume.html`, `workspace_mcp/styles.css`

## 日志查看

日志文件保存在 `logs/` 目录中：
- 主日志文件: `logs/agent_demo.log`
- 历史日志: `logs/agent_demo.log.YYYY-MM-DD`

## 注意事项

1. 确保大模型API配置正确且有足够配额
2. 任务步骤需要按顺序执行
3. 每次重置任务后需要重新从步骤1开始
4. 生成的文件会覆盖同名文件

## 技术架构

- **界面**: PySide6桌面应用框架
- **配置**: YAML格式配置文件，实时同步
- **日志**: Python logging模块，支持按天滚动
- **智能体**: 模块化设计，每个任务独立的智能体类
- **显示**: 增强的显示管理器，详细展示执行过程

## 新增特性

- **PySide6界面**: 原生桌面应用，无需浏览器
- **增强显示**: 显示执行的Python代码、作用、过程和结果
- **真实大模型调用**: 确保咨询大模型时真正调用API
- **统一文件输出**: 所有文件输出到workspace_mcp目录
- **优化配置同步**: 界面修改后立即同步到配置文件

项目完全按照需求实现，提供了完整的智能体全流程手动操作展示界面！
