import subprocess

def execute_python_script():
    try:
        script_path = "D:/proj/30MCP/MCPAgent/MCP_Agent/workspace_mcp/bubble_sort.py"
        # 构建命令：python <脚本路径> <参数>
        cmd = ["python", script_path]
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding="utf-8",  # 显式指定编码
            timeout=30,  # 设置超时防止阻塞
            check=True  # 返回非0状态码时抛出异常
        )
        print(f"execute_python result:   {result}")
        print(f"execute_python result.stdout  : {result.stdout}")
        print(f"execute_python result.stderr:   {result.stderr}")
        print(f"execute_python result.returncode:   {result.returncode}")
        return {
            "stdout": result.stdout if result.stdout is not None else "",
            "stderr": result.stderr if result.stderr is not None else "",
            "returncode": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {"error": "执行超时"}
    except FileNotFoundError:
        return {"error": f"文件不存在: {script_path}"}
    except subprocess.CalledProcessError as e:
        return {"error": f"执行失败（代码{e.returncode}）", "stderr": e.stderr}


execute_python_script()