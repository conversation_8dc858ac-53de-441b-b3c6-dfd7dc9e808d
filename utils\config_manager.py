"""
配置管理模块
负责读取和写入YAML配置文件
"""
import yaml
import os
from utils.logger import logger


class ConfigManager:
    def __init__(self, config_path="configs/config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                # 如果配置文件不存在，创建默认配置
                self._create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"成功加载配置文件: {self.config_path}")
                return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'llm': {
                'base_url': 'http://10.227.14.2:12434/v1',
                'api_key': 'EMPTY',
                'model': 'Qwen3-Coder',
                'temperature': 0.7,
                'max_tokens': 2000
            },
            'logging': {
                'level': 'INFO',
                'retention_days': 365,
                'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'ui': {
                'title': '智能体全流程展示界面',
                'theme': 'light',
                'auto_scroll': True
            }
        }
    
    def _create_default_config(self):
        """创建默认配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            default_config = self._get_default_config()
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"创建默认配置文件: {self.config_path}")
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {e}")
    
    def get_config(self, key_path=None):
        """获取配置值"""
        if key_path is None:
            return self.config
        
        keys = key_path.split('.')
        value = self.config
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            logger.warning(f"配置项不存在: {key_path}")
            return None
    
    def set_config(self, key_path, value):
        """设置配置值"""
        keys = key_path.split('.')
        config = self.config
        
        # 导航到目标位置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        
        # 保存到文件
        self._save_config()
        logger.info(f"更新配置: {key_path} = {value}")
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            logger.info("配置文件保存成功")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def get_llm_config(self):
        """获取大模型配置"""
        return self.get_config('llm')
    
    def update_llm_config(self, base_url=None, api_key=None, model=None, temperature=None, max_tokens=None):
        """更新大模型配置"""
        if base_url is not None:
            self.set_config('llm.base_url', base_url)
        if api_key is not None:
            self.set_config('llm.api_key', api_key)
        if model is not None:
            self.set_config('llm.model', model)
        if temperature is not None:
            self.set_config('llm.temperature', temperature)
        if max_tokens is not None:
            self.set_config('llm.max_tokens', max_tokens)


# 全局配置管理器实例
config_manager = ConfigManager()
