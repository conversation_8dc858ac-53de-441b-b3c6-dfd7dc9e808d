# 智能体全流程展示界面 - PySide6版本

这是一个基于PySide6的智能体全流程手动操作展示界面，将智能体的功能拆分为一个个小的单元，以按键的形式在桌面应用中进行控制，每次点击每个功能后，单独执行此功能，并在展示区显示任务执行的详细过程。

## 功能特性

### 界面结构
- **配置区**: 大模型的baseurl、apikey、model等配置，支持界面修改并同步到YAML配置文件
- **功能区**: 各种智能体任务的按键控制，每个按键对应一个功能单元
- **展示区**: 实时显示任务执行的详细过程和结果

### 日志系统
- 按天滚动的日志记录，保留365天
- 记录所有程序运行情况
- 详细的任务执行日志

### 智能体任务

#### 任务1: 大模型调用示例
- 测试大模型接口连通性
- 验证配置是否正确

#### 任务2: 冒泡排序智能体
将智能体完成冒泡排序任务的全流程拆分为7个步骤：
1. **制作任务计划**: 智能体分析任务并制作详细计划
2. **咨询大模型-算法实现**: 向大模型询问冒泡排序算法原理
3. **分析大模型返回**: 智能体分析大模型返回的信息
4. **咨询大模型-代码结构**: 向大模型询问具体的代码实现
5. **生成代码文件**: 调用工具生成Python代码文件
6. **代码验证和测试**: 验证生成的代码语法和结构
7. **任务完成总结**: 生成任务执行总结

#### 任务3: HTML文件制作
智能体完成HTML文件制作任务，包含7个步骤：
1. **分析HTML需求**: 分析要制作的HTML页面需求
2. **咨询大模型-HTML结构**: 获取HTML页面结构设计
3. **咨询大模型-样式设计**: 获取CSS样式设计
4. **生成HTML文件**: 生成HTML文件
5. **生成CSS样式文件**: 生成CSS样式文件
6. **验证文件完整性**: 验证生成的文件
7. **任务完成总结**: 生成任务执行总结

#### 任务4: 预留功能
预留的功能按键，暂未实现具体功能

## 安装和运行

### 环境要求
- Python 3.7+
- PySide6 6.6.0
- PyYAML 6.0.1
- requests 2.31.0

### 安装步骤

1. 克隆或下载项目到本地
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

3. 运行PySide6桌面应用：
```bash
python main_gui.py
```

4. 桌面应用窗口将自动打开

### 配置大模型

在桌面应用的配置区设置：
- **Base URL**: http://***********:12434/v1 (默认配置)
- **API Key**: EMPTY (默认配置)
- **Model**: Qwen3-Coder (默认配置)
- **Temperature**: 生成文本的随机性 (0.7)
- **Max Tokens**: 最大生成token数 (2000)

点击"保存配置"按钮将配置保存到`configs/config.yaml`文件中。

## 项目结构

```
MCP_Agent/
├── main_gui.py           # PySide6主启动脚本
├── requirements.txt      # 依赖包列表
├── README.md            # 详细说明文档
├── 启动说明.md           # 快速启动指南
├── test_setup.py        # 项目测试脚本
├── configs/             # 配置文件目录
│   └── config.yaml      # 主配置文件
├── logs/                # 日志文件目录
├── workspace_mcp/       # 生成文件输出目录
├── gui/                 # PySide6界面模块
│   ├── __init__.py
│   └── main_window.py   # 主窗口界面
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── logger.py        # 日志管理
│   ├── config_manager.py # 配置管理
│   └── display_manager.py # 显示管理
└── agents/              # 智能体模块
    ├── __init__.py
    ├── llm_client.py    # 大模型客户端
    ├── bubble_sort_agent.py  # 冒泡排序智能体
    └── html_agent.py    # HTML制作智能体
```

## 使用说明

1. **配置设置**: 首先在配置区设置大模型的相关参数
2. **测试连接**: 点击"测试大模型连接"验证配置是否正确
3. **执行任务**: 选择要执行的智能体任务，按步骤点击按钮
4. **观察过程**: 在展示区查看任务执行的详细过程
5. **查看日志**: 所有操作都会记录在logs目录的日志文件中

## 特色功能

- **步骤化执行**: 将复杂的智能体任务分解为多个可控步骤
- **实时展示**: 每个步骤的执行过程和结果都会实时显示
- **详细日志**: 完整记录智能体的工作流程和大模型交互
- **配置管理**: 界面化的配置管理，修改即时生效
- **响应式设计**: 支持桌面和移动设备访问

## 注意事项

- 确保大模型API配置正确且有足够的配额
- 任务步骤需要按顺序执行
- 生成的文件会保存在workspace_mcp目录中
- 日志文件会自动按天滚动，保留365天
