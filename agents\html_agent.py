"""
HTML文件制作智能体模块
智能体完成HTML文件制作任务
"""
import os
from agents.llm_client import llm_client
from utils.logger import logger, logger_manager


class HTMLAgent:
    def __init__(self):
        self.task_name = "HTML文件制作任务"
        self.current_step = 0
        self.steps = [
            "分析HTML需求",
            "咨询大模型-HTML结构",
            "咨询大模型-样式设计",
            "生成HTML文件",
            "生成CSS样式文件",
            "验证文件完整性",
            "任务完成总结"
        ]
        self.step_results = {}
        self.requirements = ""
        self.html_structure = ""
        self.css_styles = ""
        self.generated_html = ""
        self.generated_css = ""
    
    def reset_task(self):
        """重置任务状态"""
        self.current_step = 0
        self.step_results = {}
        self.requirements = ""
        self.html_structure = ""
        self.css_styles = ""
        self.generated_html = ""
        self.generated_css = ""
        logger.info("HTML制作任务已重置")
    
    def get_current_step_info(self):
        """获取当前步骤信息"""
        if self.current_step < len(self.steps):
            return {
                'step_number': self.current_step + 1,
                'step_name': self.steps[self.current_step],
                'total_steps': len(self.steps),
                'is_completed': False
            }
        else:
            return {
                'step_number': len(self.steps),
                'step_name': '任务已完成',
                'total_steps': len(self.steps),
                'is_completed': True
            }
    
    def step_1_analyze_requirements(self):
        """步骤1: 分析HTML需求"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤1: 分析HTML需求")
        
        try:
            # 智能体分析HTML制作需求
            prompt = """作为一个智能体，我需要制作一个HTML页面。请帮我分析HTML文件制作的需求，包括：
1. 页面的基本结构
2. 需要包含的主要内容模块
3. 样式设计要求
4. 技术实现要点
请提供一个详细的需求分析。"""

            success, response = llm_client.call_llm(prompt)

            if success:
                self.requirements = response
            else:
                # 如果大模型调用失败，使用备用需求
                self.requirements = """
HTML文件制作需求分析：
1. 创建一个响应式的个人简历页面
2. 包含头部、导航、主要内容和页脚
3. 使用现代CSS样式
4. 包含个人信息、技能、经验等模块
5. 支持移动端显示
6. 使用语义化HTML标签
                """.strip()
            
            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'result': self.requirements,
                'success': True
            }
            
            logger_manager.log_step("需求分析", "HTML制作需求分析完成")
            self.current_step += 1
            
            return True, self.requirements
            
        except Exception as e:
            error_msg = f"需求分析失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def step_2_consult_html_structure(self):
        """步骤2: 咨询大模型-HTML结构"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤2: 咨询大模型HTML结构")
        
        try:
            prompt = f"""基于以下需求，请设计一个完整的HTML页面结构：

需求：
{self.requirements}

请提供：
1. 完整的HTML5文档结构
2. 使用语义化标签
3. 包含个人简历的各个部分
4. 添加适当的class和id属性
5. 预留CSS样式的钩子

请直接提供完整的HTML代码。"""
            
            success, response = llm_client.call_llm(prompt)
            
            if success:
                self.html_structure = response
                self.step_results[self.current_step] = {
                    'step_name': self.steps[self.current_step],
                    'prompt': prompt,
                    'result': response,
                    'success': True
                }
                
                logger_manager.log_step("咨询HTML结构", "大模型返回HTML结构")
                self.current_step += 1
                return True, response
            else:
                return False, response
                
        except Exception as e:
            error_msg = f"咨询HTML结构失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def step_3_consult_css_styles(self):
        """步骤3: 咨询大模型-样式设计"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤3: 咨询大模型样式设计")
        
        try:
            prompt = f"""基于以下HTML结构，请设计相应的CSS样式：

HTML结构（前500字符）：
{self.html_structure[:700]}...

请提供：
1. 现代化的CSS样式
2. 响应式设计
3. 美观的颜色搭配
4. 合适的字体和间距
5. 移动端适配

请直接提供完整的CSS代码。"""
            
            success, response = llm_client.call_llm(prompt)
            
            if success:
                self.css_styles = response
                self.step_results[self.current_step] = {
                    'step_name': self.steps[self.current_step],
                    'prompt': prompt,
                    'result': response,
                    'success': True
                }
                
                logger_manager.log_step("咨询CSS样式", "大模型返回CSS样式")
                self.current_step += 1
                return True, response
            else:
                return False, response
                
        except Exception as e:
            error_msg = f"咨询CSS样式失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def step_4_generate_html_file(self):
        """步骤4: 生成HTML文件"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤4: 生成HTML文件")
        
        try:
            if not self.html_structure:
                return False, "没有HTML结构可供生成文件"
            
            # 确保输出目录存在
            os.makedirs('workspace_mcp', exist_ok=True)

            # 生成文件名
            filename = 'workspace_mcp/resume.html'
            
            # 写入HTML文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.html_structure)
            
            self.generated_html = self.html_structure
            
            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'filename': filename,
                'file_size': len(self.html_structure),
                'success': True
            }
            
            logger_manager.log_tool_call("文件生成", f"写入HTML文件: {filename}", f"文件大小: {len(self.html_structure)}")
            logger_manager.log_step("生成HTML文件", f"文件已保存: {filename}")
            self.current_step += 1
            
            return True, f"HTML文件已生成: {filename}\n文件大小: {len(self.html_structure)} 字符"
            
        except Exception as e:
            error_msg = f"生成HTML文件失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def step_5_generate_css_file(self):
        """步骤5: 生成CSS样式文件"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤5: 生成CSS样式文件")
        
        try:
            if not self.css_styles:
                return False, "没有CSS样式可供生成文件"
            
            # 确保输出目录存在
            os.makedirs('workspace_mcp', exist_ok=True)

            # 生成文件名
            filename = 'workspace_mcp/styles.css'
            
            # 写入CSS文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.css_styles)
            
            self.generated_css = self.css_styles
            
            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'filename': filename,
                'file_size': len(self.css_styles),
                'success': True
            }
            
            logger_manager.log_tool_call("文件生成", f"写入CSS文件: {filename}", f"文件大小: {len(self.css_styles)}")
            logger_manager.log_step("生成CSS文件", f"文件已保存: {filename}")
            self.current_step += 1
            
            return True, f"CSS文件已生成: {filename}\n文件大小: {len(self.css_styles)} 字符"
            
        except Exception as e:
            error_msg = f"生成CSS文件失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def step_6_verify_files(self):
        """步骤6: 验证文件完整性"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤6: 验证文件完整性")

        try:
            html_file = 'workspace_mcp/resume.html'
            css_file = 'workspace_mcp/styles.css'

            verification_result = "文件验证结果：\n"

            # 检查HTML文件
            if os.path.exists(html_file):
                html_size = os.path.getsize(html_file)
                verification_result += f"- HTML文件: {html_file} (大小: {html_size} 字节) ✓\n"

                # 简单的HTML内容检查
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                    if '<html' in html_content and '</html>' in html_content:
                        verification_result += "- HTML结构完整 ✓\n"
                    else:
                        verification_result += "- HTML结构不完整 ✗\n"
            else:
                verification_result += f"- HTML文件不存在: {html_file} ✗\n"

            # 检查CSS文件
            if os.path.exists(css_file):
                css_size = os.path.getsize(css_file)
                verification_result += f"- CSS文件: {css_file} (大小: {css_size} 字节) ✓\n"
            else:
                verification_result += f"- CSS文件不存在: {css_file} ✗\n"

            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'html_file': html_file,
                'css_file': css_file,
                'verification_result': verification_result,
                'success': True
            }

            logger_manager.log_step("文件验证", verification_result)
            self.current_step += 1

            return True, verification_result

        except Exception as e:
            error_msg = f"文件验证失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def step_7_task_summary(self):
        """步骤7: 任务完成总结"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤7: 任务完成总结")

        try:
            # 生成任务总结
            summary = f"""
=== HTML文件制作任务完成总结 ===

任务执行步骤：
"""

            for i, (step_idx, result) in enumerate(self.step_results.items()):
                summary += f"{i+1}. {result['step_name']}: {'成功' if result['success'] else '失败'}\n"

            summary += f"""
最终成果：
- HTML文件: workspace_mcp/resume.html
- CSS文件: workspace_mcp/styles.css
- HTML代码长度: {len(self.generated_html)} 字符
- CSS代码长度: {len(self.generated_css)} 字符
- 任务状态: 已完成

智能体工作流程：
1. 分析HTML制作需求
2. 咨询大模型获取HTML结构设计
3. 咨询大模型获取CSS样式设计
4. 生成HTML文件
5. 生成CSS样式文件
6. 验证文件完整性和结构
7. 完成任务总结

本次演示展示了智能体如何：
- 分析前端开发需求
- 与大模型协作设计页面结构
- 分别处理HTML和CSS内容
- 生成多个相关文件
- 验证文件的完整性
            """.strip()

            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'summary': summary,
                'total_steps_completed': len(self.step_results),
                'success': True
            }

            logger_manager.log_task_end(self.task_name, True)
            logger_manager.log_step("任务总结", "HTML制作任务全流程完成")
            self.current_step += 1

            return True, summary

        except Exception as e:
            error_msg = f"任务总结失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def execute_current_step(self):
        """执行当前步骤"""
        if self.current_step >= len(self.steps):
            return False, "所有步骤已完成"

        step_methods = [
            self.step_1_analyze_requirements,
            self.step_2_consult_html_structure,
            self.step_3_consult_css_styles,
            self.step_4_generate_html_file,
            self.step_5_generate_css_file,
            self.step_6_verify_files,
            self.step_7_task_summary
        ]

        try:
            return step_methods[self.current_step]()
        except Exception as e:
            error_msg = f"执行步骤失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_all_results(self):
        """获取所有步骤的结果"""
        return self.step_results


# 全局HTML智能体实例
html_agent = HTMLAgent()
