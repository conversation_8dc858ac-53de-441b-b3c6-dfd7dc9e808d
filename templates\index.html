<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体全流程展示界面</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 标题区域 -->
        <header class="header">
            <h1>智能体全流程展示界面</h1>
            <p>手动控制智能体的各个功能单元，观察任务执行的详细过程</p>
        </header>

        <!-- 配置区域 -->
        <section class="config-section">
            <h2>配置区</h2>
            <div class="config-grid">
                <div class="config-item">
                    <label for="base_url">Base URL:</label>
                    <input type="text" id="base_url" placeholder="https://api.openai.com/v1">
                </div>
                <div class="config-item">
                    <label for="api_key">API Key:</label>
                    <input type="password" id="api_key" placeholder="your-api-key-here">
                </div>
                <div class="config-item">
                    <label for="model">Model:</label>
                    <select id="model">
                        <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
                        <option value="gpt-4">gpt-4</option>
                        <option value="gpt-4-turbo">gpt-4-turbo</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="temperature">Temperature:</label>
                    <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.7">
                </div>
                <div class="config-item">
                    <label for="max_tokens">Max Tokens:</label>
                    <input type="number" id="max_tokens" min="1" max="4000" value="2000">
                </div>
                <div class="config-item">
                    <button id="save_config" class="btn btn-primary">保存配置</button>
                    <button id="load_config" class="btn btn-secondary">加载配置</button>
                </div>
            </div>
        </section>

        <!-- 功能区域 -->
        <section class="function-section">
            <h2>功能区</h2>
            
            <!-- 任务1: 大模型调用示例 -->
            <div class="task-group">
                <h3>任务1: 大模型调用示例</h3>
                <div class="button-group">
                    <button id="test_llm" class="btn btn-test">测试大模型连接</button>
                </div>
            </div>

            <!-- 任务2: 冒泡排序智能体 -->
            <div class="task-group">
                <h3>任务2: 冒泡排序智能体</h3>
                <div class="button-group">
                    <button id="bubble_reset" class="btn btn-secondary">重置任务</button>
                    <button id="bubble_step1" class="btn btn-step">步骤1: 制作计划</button>
                    <button id="bubble_step2" class="btn btn-step">步骤2: 咨询算法</button>
                    <button id="bubble_step3" class="btn btn-step">步骤3: 分析返回</button>
                    <button id="bubble_step4" class="btn btn-step">步骤4: 咨询代码</button>
                    <button id="bubble_step5" class="btn btn-step">步骤5: 生成文件</button>
                    <button id="bubble_step6" class="btn btn-step">步骤6: 验证代码</button>
                    <button id="bubble_step7" class="btn btn-step">步骤7: 任务总结</button>
                </div>
                <div class="step-info">
                    <span id="bubble_current_step">当前步骤: 未开始</span>
                </div>
            </div>

            <!-- 任务3: HTML文件制作 -->
            <div class="task-group">
                <h3>任务3: HTML文件制作</h3>
                <div class="button-group">
                    <button id="html_reset" class="btn btn-secondary">重置任务</button>
                    <button id="html_step1" class="btn btn-step">步骤1: 需求分析</button>
                    <button id="html_step2" class="btn btn-step">步骤2: HTML结构</button>
                    <button id="html_step3" class="btn btn-step">步骤3: CSS样式</button>
                    <button id="html_step4" class="btn btn-step">步骤4: 生成HTML</button>
                    <button id="html_step5" class="btn btn-step">步骤5: 生成CSS</button>
                    <button id="html_step6" class="btn btn-step">步骤6: 验证文件</button>
                    <button id="html_step7" class="btn btn-step">步骤7: 任务总结</button>
                </div>
                <div class="step-info">
                    <span id="html_current_step">当前步骤: 未开始</span>
                </div>
            </div>

            <!-- 任务4: 预留功能 -->
            <div class="task-group">
                <h3>任务4: 预留功能</h3>
                <div class="button-group">
                    <button id="task4_placeholder" class="btn btn-disabled" disabled>功能开发中...</button>
                </div>
            </div>
        </section>

        <!-- 展示区域 -->
        <section class="display-section">
            <h2>展示区</h2>
            <div class="display-content">
                <div class="display-header">
                    <span id="current_task">当前任务: 无</span>
                    <button id="clear_display" class="btn btn-small">清空显示</button>
                </div>
                <div id="display_area" class="display-area">
                    <p class="welcome-message">欢迎使用智能体全流程展示界面！</p>
                    <p>请选择上方的功能按钮开始操作。</p>
                </div>
            </div>
        </section>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>正在执行中，请稍候...</p>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
