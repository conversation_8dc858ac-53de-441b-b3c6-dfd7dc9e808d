<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="129">
            <item index="0" class="java.lang.String" itemvalue="tiktoken" />
            <item index="1" class="java.lang.String" itemvalue="langfuse" />
            <item index="2" class="java.lang.String" itemvalue="wheel" />
            <item index="3" class="java.lang.String" itemvalue="setuptools" />
            <item index="4" class="java.lang.String" itemvalue="bottle" />
            <item index="5" class="java.lang.String" itemvalue="requests" />
            <item index="6" class="java.lang.String" itemvalue="crewai" />
            <item index="7" class="java.lang.String" itemvalue="gymnasium" />
            <item index="8" class="java.lang.String" itemvalue="pillow" />
            <item index="9" class="java.lang.String" itemvalue="datasets" />
            <item index="10" class="java.lang.String" itemvalue="mcp" />
            <item index="11" class="java.lang.String" itemvalue="opencv-python" />
            <item index="12" class="java.lang.String" itemvalue="numba" />
            <item index="13" class="java.lang.String" itemvalue="termios" />
            <item index="14" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="15" class="java.lang.String" itemvalue="tabulate" />
            <item index="16" class="java.lang.String" itemvalue="cython" />
            <item index="17" class="java.lang.String" itemvalue="csv" />
            <item index="18" class="java.lang.String" itemvalue="redis" />
            <item index="19" class="java.lang.String" itemvalue="python-magic" />
            <item index="20" class="java.lang.String" itemvalue="collections" />
            <item index="21" class="java.lang.String" itemvalue="starlette" />
            <item index="22" class="java.lang.String" itemvalue="xml" />
            <item index="23" class="java.lang.String" itemvalue="gettext" />
            <item index="24" class="java.lang.String" itemvalue="ast-tools" />
            <item index="25" class="java.lang.String" itemvalue="lxml" />
            <item index="26" class="java.lang.String" itemvalue="PyPDF2" />
            <item index="27" class="java.lang.String" itemvalue="uvicorn" />
            <item index="28" class="java.lang.String" itemvalue="pydantic" />
            <item index="29" class="java.lang.String" itemvalue="transformers" />
            <item index="30" class="java.lang.String" itemvalue="zipfile" />
            <item index="31" class="java.lang.String" itemvalue="errno" />
            <item index="32" class="java.lang.String" itemvalue="hashlib" />
            <item index="33" class="java.lang.String" itemvalue="codecs" />
            <item index="34" class="java.lang.String" itemvalue="pkg-resources" />
            <item index="35" class="java.lang.String" itemvalue="subprocess" />
            <item index="36" class="java.lang.String" itemvalue="click" />
            <item index="37" class="java.lang.String" itemvalue="openai" />
            <item index="38" class="java.lang.String" itemvalue="regex" />
            <item index="39" class="java.lang.String" itemvalue="bisect" />
            <item index="40" class="java.lang.String" itemvalue="re" />
            <item index="41" class="java.lang.String" itemvalue="functools" />
            <item index="42" class="java.lang.String" itemvalue="matplotlib" />
            <item index="43" class="java.lang.String" itemvalue="math" />
            <item index="44" class="java.lang.String" itemvalue="pwd" />
            <item index="45" class="java.lang.String" itemvalue="concurrent-futures" />
            <item index="46" class="java.lang.String" itemvalue="pympler" />
            <item index="47" class="java.lang.String" itemvalue="binascii" />
            <item index="48" class="java.lang.String" itemvalue="numpy" />
            <item index="49" class="java.lang.String" itemvalue="pickle" />
            <item index="50" class="java.lang.String" itemvalue="celery" />
            <item index="51" class="java.lang.String" itemvalue="selenium" />
            <item index="52" class="java.lang.String" itemvalue="prompt-toolkit" />
            <item index="53" class="java.lang.String" itemvalue="copy" />
            <item index="54" class="java.lang.String" itemvalue="calendar" />
            <item index="55" class="java.lang.String" itemvalue="black" />
            <item index="56" class="java.lang.String" itemvalue="statsd" />
            <item index="57" class="java.lang.String" itemvalue="aiofiles" />
            <item index="58" class="java.lang.String" itemvalue="tempfile" />
            <item index="59" class="java.lang.String" itemvalue="plotly" />
            <item index="60" class="java.lang.String" itemvalue="gzip" />
            <item index="61" class="java.lang.String" itemvalue="pytest-asyncio" />
            <item index="62" class="java.lang.String" itemvalue="toml" />
            <item index="63" class="java.lang.String" itemvalue="pandas" />
            <item index="64" class="java.lang.String" itemvalue="pylint" />
            <item index="65" class="java.lang.String" itemvalue="unicodedata" />
            <item index="66" class="java.lang.String" itemvalue="cachetools" />
            <item index="67" class="java.lang.String" itemvalue="babel" />
            <item index="68" class="java.lang.String" itemvalue="threading" />
            <item index="69" class="java.lang.String" itemvalue="ujson" />
            <item index="70" class="java.lang.String" itemvalue="Pillow" />
            <item index="71" class="java.lang.String" itemvalue="statistics" />
            <item index="72" class="java.lang.String" itemvalue="mypy" />
            <item index="73" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="74" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="75" class="java.lang.String" itemvalue="base64" />
            <item index="76" class="java.lang.String" itemvalue="multiprocessing" />
            <item index="77" class="java.lang.String" itemvalue="unittest" />
            <item index="78" class="java.lang.String" itemvalue="kombu" />
            <item index="79" class="java.lang.String" itemvalue="operator" />
            <item index="80" class="java.lang.String" itemvalue="datetime" />
            <item index="81" class="java.lang.String" itemvalue="pathlib" />
            <item index="82" class="java.lang.String" itemvalue="urllib" />
            <item index="83" class="java.lang.String" itemvalue="anthropic" />
            <item index="84" class="java.lang.String" itemvalue="signal" />
            <item index="85" class="java.lang.String" itemvalue="traceback" />
            <item index="86" class="java.lang.String" itemvalue="memory-profiler" />
            <item index="87" class="java.lang.String" itemvalue="dnspython" />
            <item index="88" class="java.lang.String" itemvalue="cryptography" />
            <item index="89" class="java.lang.String" itemvalue="asyncio-mqtt" />
            <item index="90" class="java.lang.String" itemvalue="secrets" />
            <item index="91" class="java.lang.String" itemvalue="jinja2" />
            <item index="92" class="java.lang.String" itemvalue="configparser" />
            <item index="93" class="java.lang.String" itemvalue="http" />
            <item index="94" class="java.lang.String" itemvalue="sqlite3" />
            <item index="95" class="java.lang.String" itemvalue="string" />
            <item index="96" class="java.lang.String" itemvalue="doctest" />
            <item index="97" class="java.lang.String" itemvalue="sys" />
            <item index="98" class="java.lang.String" itemvalue="locale" />
            <item index="99" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="100" class="java.lang.String" itemvalue="itertools" />
            <item index="101" class="java.lang.String" itemvalue="flake8" />
            <item index="102" class="java.lang.String" itemvalue="platform" />
            <item index="103" class="java.lang.String" itemvalue="random" />
            <item index="104" class="java.lang.String" itemvalue="pytest-cov" />
            <item index="105" class="java.lang.String" itemvalue="aiosqlite" />
            <item index="106" class="java.lang.String" itemvalue="hmac" />
            <item index="107" class="java.lang.String" itemvalue="json" />
            <item index="108" class="java.lang.String" itemvalue="urllib3" />
            <item index="109" class="java.lang.String" itemvalue="gc" />
            <item index="110" class="java.lang.String" itemvalue="python-docx" />
            <item index="111" class="java.lang.String" itemvalue="os" />
            <item index="112" class="java.lang.String" itemvalue="grp" />
            <item index="113" class="java.lang.String" itemvalue="pytest" />
            <item index="114" class="java.lang.String" itemvalue="warnings" />
            <item index="115" class="java.lang.String" itemvalue="rich" />
            <item index="116" class="java.lang.String" itemvalue="dill" />
            <item index="117" class="java.lang.String" itemvalue="prometheus-client" />
            <item index="118" class="java.lang.String" itemvalue="heapq" />
            <item index="119" class="java.lang.String" itemvalue="tarfile" />
            <item index="120" class="java.lang.String" itemvalue="pdb" />
            <item index="121" class="java.lang.String" itemvalue="tqdm" />
            <item index="122" class="java.lang.String" itemvalue="fastapi" />
            <item index="123" class="java.lang.String" itemvalue="weakref" />
            <item index="124" class="java.lang.String" itemvalue="tty" />
            <item index="125" class="java.lang.String" itemvalue="shutil" />
            <item index="126" class="java.lang.String" itemvalue="time" />
            <item index="127" class="java.lang.String" itemvalue="socket" />
            <item index="128" class="java.lang.String" itemvalue="mmap" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>