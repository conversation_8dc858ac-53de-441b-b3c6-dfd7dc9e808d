"""
智能体全流程展示界面 - PySide6主窗口
"""
import sys
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QGridLayout, QLabel, QLineEdit, 
                               QPushButton, QTextEdit, QComboBox, QDoubleSpinBox,
                               QSpinBox, QGroupBox, QScrollArea, QSplitter,
                               QProgressBar, QMessageBox)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QTextCursor

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_manager import config_manager
from utils.logger import logger, logger_manager
from utils.display_manager import display_manager
from agents.llm_client import llm_client
from agents.bubble_sort_agent import bubble_sort_agent
from agents.html_agent import html_agent


class TaskWorker(QThread):
    """任务执行工作线程"""
    finished = Signal(bool, str, str)  # success, result, step_name
    progress = Signal(str)  # progress message
    
    def __init__(self, task_type, step_number=None, action="execute"):
        super().__init__()
        self.task_type = task_type
        self.step_number = step_number
        self.action = action
    
    def run(self):
        try:
            if self.action == "test_llm":
                self.progress.emit("正在测试大模型连接...")
                success, message = llm_client.test_connection()
                self.finished.emit(success, message, "大模型连接测试")
                
            elif self.action == "reset":
                self.progress.emit(f"正在重置{self.task_type}任务...")
                if self.task_type == "bubble_sort":
                    bubble_sort_agent.reset_task()
                elif self.task_type == "html":
                    html_agent.reset_task()
                self.finished.emit(True, "任务已重置", "重置任务")
                
            elif self.action == "execute":
                agent = bubble_sort_agent if self.task_type == "bubble_sort" else html_agent
                task_name = "冒泡排序任务" if self.task_type == "bubble_sort" else "HTML制作任务"
                
                self.progress.emit(f"正在执行{task_name}步骤{self.step_number}...")
                success, result = agent.execute_current_step()
                step_name = agent.steps[self.step_number - 1] if self.step_number <= len(agent.steps) else f"步骤{self.step_number}"
                
                self.finished.emit(success, result, step_name)
                
        except Exception as e:
            self.finished.emit(False, f"执行失败: {str(e)}", "错误")


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能体全流程展示界面")
        self.setGeometry(100, 100, 1400, 900)
        
        # 确保必要的目录存在
        os.makedirs('logs', exist_ok=True)
        os.makedirs('workspace_mcp', exist_ok=True)
        os.makedirs('configs', exist_ok=True)
        
        self.init_ui()
        self.load_config()

        # 设置显示管理器的回调
        display_manager.set_display_callback(self.append_to_display)

        # 定时器用于更新步骤状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_step_status)
        self.status_timer.start(1000)  # 每秒更新一次

        logger.info("PySide6界面启动成功")
    
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("智能体全流程展示界面")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px; padding: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板（配置区和功能区）
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 配置区
        self.create_config_section(left_layout)
        
        # 功能区
        self.create_function_section(left_layout)
        
        splitter.addWidget(left_panel)
        
        # 右侧面板（展示区）
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 展示区
        self.create_display_section(right_layout)
        
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([500, 900])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_config_section(self, parent_layout):
        """创建配置区"""
        config_group = QGroupBox("配置区")
        config_layout = QGridLayout(config_group)
        
        # Base URL
        config_layout.addWidget(QLabel("Base URL:"), 0, 0)
        self.base_url_edit = QLineEdit()
        self.base_url_edit.setPlaceholderText("http://10.227.14.2:12434/v1")
        config_layout.addWidget(self.base_url_edit, 0, 1)
        
        # API Key
        config_layout.addWidget(QLabel("API Key:"), 1, 0)
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setPlaceholderText("EMPTY")
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        config_layout.addWidget(self.api_key_edit, 1, 1)
        
        # Model
        config_layout.addWidget(QLabel("Model:"), 2, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(["Qwen3-Coder", "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"])
        config_layout.addWidget(self.model_combo, 2, 1)
        
        # Temperature
        config_layout.addWidget(QLabel("Temperature:"), 3, 0)
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.7)
        config_layout.addWidget(self.temperature_spin, 3, 1)
        
        # Max Tokens
        config_layout.addWidget(QLabel("Max Tokens:"), 4, 0)
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(1, 8000)
        self.max_tokens_spin.setValue(2000)
        config_layout.addWidget(self.max_tokens_spin, 4, 1)
        
        # 配置按钮
        config_btn_layout = QHBoxLayout()
        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.clicked.connect(self.save_config)
        self.load_config_btn = QPushButton("加载配置")
        self.load_config_btn.clicked.connect(self.load_config)
        
        config_btn_layout.addWidget(self.save_config_btn)
        config_btn_layout.addWidget(self.load_config_btn)
        config_layout.addLayout(config_btn_layout, 5, 0, 1, 2)
        
        parent_layout.addWidget(config_group)
    
    def create_function_section(self, parent_layout):
        """创建功能区"""
        function_group = QGroupBox("功能区")
        function_layout = QVBoxLayout(function_group)
        
        # 任务1: 大模型调用示例
        task1_group = QGroupBox("任务1: 大模型调用示例")
        task1_layout = QHBoxLayout(task1_group)
        
        self.test_llm_btn = QPushButton("测试大模型连接")
        self.test_llm_btn.clicked.connect(self.test_llm_connection)
        task1_layout.addWidget(self.test_llm_btn)
        
        function_layout.addWidget(task1_group)
        
        # 任务2: 冒泡排序智能体
        self.create_bubble_sort_section(function_layout)
        
        # 任务3: HTML文件制作
        self.create_html_section(function_layout)
        
        # 任务4: 预留功能
        task4_group = QGroupBox("任务4: 预留功能")
        task4_layout = QHBoxLayout(task4_group)
        
        placeholder_btn = QPushButton("功能开发中...")
        placeholder_btn.setEnabled(False)
        task4_layout.addWidget(placeholder_btn)
        
        function_layout.addWidget(task4_group)
        
        parent_layout.addWidget(function_group)

    def create_bubble_sort_section(self, parent_layout):
        """创建冒泡排序任务区"""
        bubble_group = QGroupBox("任务2: 冒泡排序智能体")
        bubble_layout = QVBoxLayout(bubble_group)

        # 按钮布局
        btn_layout = QGridLayout()

        # 重置按钮
        self.bubble_reset_btn = QPushButton("重置任务")
        self.bubble_reset_btn.clicked.connect(lambda: self.reset_task("bubble_sort"))
        btn_layout.addWidget(self.bubble_reset_btn, 0, 0)

        # 步骤按钮
        self.bubble_step_btns = []
        step_names = [
            "步骤1: 制作计划", "步骤2: 咨询算法", "步骤3: 分析返回", "步骤4: 咨询代码",
            "步骤5: 生成文件", "步骤6: 验证代码", "步骤7: 任务总结"
        ]

        for i, name in enumerate(step_names):
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, step=i+1: self.execute_bubble_step(step))
            self.bubble_step_btns.append(btn)
            row = (i + 1) // 2
            col = (i + 1) % 2
            btn_layout.addWidget(btn, row, col)

        bubble_layout.addLayout(btn_layout)

        # 状态显示
        self.bubble_status_label = QLabel("当前步骤: 未开始")
        self.bubble_status_label.setStyleSheet("color: #2980b9; font-weight: bold; padding: 5px;")
        bubble_layout.addWidget(self.bubble_status_label)

        parent_layout.addWidget(bubble_group)

    def create_html_section(self, parent_layout):
        """创建HTML任务区"""
        html_group = QGroupBox("任务3: HTML文件制作")
        html_layout = QVBoxLayout(html_group)

        # 按钮布局
        btn_layout = QGridLayout()

        # 重置按钮
        self.html_reset_btn = QPushButton("重置任务")
        self.html_reset_btn.clicked.connect(lambda: self.reset_task("html"))
        btn_layout.addWidget(self.html_reset_btn, 0, 0)

        # 步骤按钮
        self.html_step_btns = []
        step_names = [
            "步骤1: 需求分析", "步骤2: HTML结构", "步骤3: CSS样式", "步骤4: 生成HTML",
            "步骤5: 生成CSS", "步骤6: 验证文件", "步骤7: 任务总结"
        ]

        for i, name in enumerate(step_names):
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, step=i+1: self.execute_html_step(step))
            self.html_step_btns.append(btn)
            row = (i + 1) // 2
            col = (i + 1) % 2
            btn_layout.addWidget(btn, row, col)

        html_layout.addLayout(btn_layout)

        # 状态显示
        self.html_status_label = QLabel("当前步骤: 未开始")
        self.html_status_label.setStyleSheet("color: #2980b9; font-weight: bold; padding: 5px;")
        html_layout.addWidget(self.html_status_label)

        parent_layout.addWidget(html_group)

    def create_display_section(self, parent_layout):
        """创建展示区"""
        display_group = QGroupBox("展示区")
        display_layout = QVBoxLayout(display_group)

        # 展示区头部
        header_layout = QHBoxLayout()

        self.current_task_label = QLabel("当前任务: 无")
        self.current_task_label.setStyleSheet("font-weight: bold; color: #34495e;")
        header_layout.addWidget(self.current_task_label)

        header_layout.addStretch()

        self.clear_display_btn = QPushButton("清空显示")
        self.clear_display_btn.clicked.connect(self.clear_display)
        header_layout.addWidget(self.clear_display_btn)

        display_layout.addLayout(header_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        display_layout.addWidget(self.progress_bar)

        # 展示文本区域
        self.display_text = QTextEdit()
        self.display_text.setReadOnly(True)
        self.display_text.setFont(QFont("Consolas", 10))
        self.display_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)

        # 初始欢迎信息
        welcome_text = """欢迎使用智能体全流程展示界面！

请按照以下步骤开始使用：
1. 在配置区设置大模型的相关参数
2. 点击"保存配置"保存设置
3. 点击"测试大模型连接"验证配置
4. 选择要执行的智能体任务
5. 按步骤点击按钮，观察执行过程

功能说明：
- 任务1: 测试大模型连接，询问什么是MCP
- 任务2: 冒泡排序智能体，7个步骤完成排序算法实现
- 任务3: HTML文件制作，7个步骤完成网页制作
- 任务4: 预留功能，暂未实现

所有生成的文件将保存在 workspace_mcp 文件夹中。
"""
        self.display_text.setPlainText(welcome_text)

        display_layout.addWidget(self.display_text)

        parent_layout.addWidget(display_group)

    def save_config(self):
        """保存配置"""
        try:
            config_manager.update_llm_config(
                base_url=self.base_url_edit.text(),
                api_key=self.api_key_edit.text(),
                model=self.model_combo.currentText(),
                temperature=self.temperature_spin.value(),
                max_tokens=self.max_tokens_spin.value()
            )

            # 更新LLM客户端配置
            llm_client.update_config()

            self.append_to_display("配置保存", "配置已成功保存到文件", "success")
            self.statusBar().showMessage("配置保存成功", 3000)

        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            self.append_to_display("配置保存", error_msg, "error")
            QMessageBox.warning(self, "错误", error_msg)

    def load_config(self):
        """加载配置"""
        try:
            config = config_manager.get_llm_config()

            self.base_url_edit.setText(config.get('base_url', ''))
            self.api_key_edit.setText(config.get('api_key', ''))

            model = config.get('model', 'Qwen3-Coder')
            index = self.model_combo.findText(model)
            if index >= 0:
                self.model_combo.setCurrentIndex(index)

            self.temperature_spin.setValue(config.get('temperature', 0.7))
            self.max_tokens_spin.setValue(config.get('max_tokens', 2000))

            self.append_to_display("配置加载", "配置已从文件加载", "success")
            self.statusBar().showMessage("配置加载成功", 3000)

        except Exception as e:
            error_msg = f"加载配置失败: {str(e)}"
            self.append_to_display("配置加载", error_msg, "error")
            QMessageBox.warning(self, "错误", error_msg)

    def test_llm_connection(self):
        """测试大模型连接"""
        self.current_task_label.setText("当前任务: 大模型连接测试")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条

        # 创建工作线程
        self.worker = TaskWorker("", action="test_llm")
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.on_task_finished)
        self.worker.start()

        # 禁用按钮
        self.test_llm_btn.setEnabled(False)

    def reset_task(self, task_type):
        """重置任务"""
        task_name = "冒泡排序任务" if task_type == "bubble_sort" else "HTML制作任务"
        self.current_task_label.setText(f"当前任务: {task_name}重置")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 创建工作线程
        self.worker = TaskWorker(task_type, action="reset")
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.on_task_finished)
        self.worker.start()

        # 禁用相关按钮
        if task_type == "bubble_sort":
            self.bubble_reset_btn.setEnabled(False)
            for btn in self.bubble_step_btns:
                btn.setEnabled(False)
        else:
            self.html_reset_btn.setEnabled(False)
            for btn in self.html_step_btns:
                btn.setEnabled(False)

    def execute_bubble_step(self, step_number):
        """执行冒泡排序步骤"""
        self.execute_step("bubble_sort", step_number)

    def execute_html_step(self, step_number):
        """执行HTML步骤"""
        self.execute_step("html", step_number)

    def execute_step(self, task_type, step_number):
        """执行任务步骤"""
        agent = bubble_sort_agent if task_type == "bubble_sort" else html_agent
        task_name = "冒泡排序任务" if task_type == "bubble_sort" else "HTML制作任务"

        # 检查步骤是否有效
        if step_number < 1 or step_number > len(agent.steps):
            QMessageBox.warning(self, "错误", "无效的步骤编号")
            return

        # 检查是否按顺序执行
        if step_number != agent.current_step + 1:
            QMessageBox.warning(self, "错误",
                              f"请按顺序执行步骤，当前应执行步骤 {agent.current_step + 1}")
            return

        self.current_task_label.setText(f"当前任务: {task_name} - 步骤{step_number}")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 创建工作线程
        self.worker = TaskWorker(task_type, step_number, "execute")
        self.worker.progress.connect(self.update_progress)
        self.worker.finished.connect(self.on_task_finished)
        self.worker.start()

        # 禁用相关按钮
        if task_type == "bubble_sort":
            for btn in self.bubble_step_btns:
                btn.setEnabled(False)
        else:
            for btn in self.html_step_btns:
                btn.setEnabled(False)

    def update_progress(self, message):
        """更新进度信息"""
        self.statusBar().showMessage(message)

    def on_task_finished(self, success, result, step_name):
        """任务完成回调"""
        self.progress_bar.setVisible(False)

        # 重新启用按钮
        self.test_llm_btn.setEnabled(True)
        self.bubble_reset_btn.setEnabled(True)
        self.html_reset_btn.setEnabled(True)
        for btn in self.bubble_step_btns:
            btn.setEnabled(True)
        for btn in self.html_step_btns:
            btn.setEnabled(True)

        # 显示结果
        status = "success" if success else "error"
        self.append_to_display(step_name, result, status)

        if success:
            self.statusBar().showMessage(f"{step_name}执行成功", 3000)
        else:
            self.statusBar().showMessage(f"{step_name}执行失败", 3000)
            QMessageBox.warning(self, "执行失败", result)

        # 更新步骤状态
        self.update_step_status()

    def update_step_status(self):
        """更新步骤状态显示"""
        try:
            # 更新冒泡排序状态
            bubble_info = bubble_sort_agent.get_current_step_info()
            status_text = f"当前步骤: {bubble_info['step_number']}/{bubble_info['total_steps']} - {bubble_info['step_name']}"
            self.bubble_status_label.setText(status_text)

            # 更新HTML状态
            html_info = html_agent.get_current_step_info()
            status_text = f"当前步骤: {html_info['step_number']}/{html_info['total_steps']} - {html_info['step_name']}"
            self.html_status_label.setText(status_text)

        except Exception as e:
            logger.error(f"更新步骤状态失败: {e}")

    def append_to_display(self, title, content, msg_type="info"):
        """添加内容到展示区"""
        import datetime

        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # 设置颜色
        color = "#27ae60" if msg_type == "success" else "#e74c3c" if msg_type == "error" else "#3498db"

        # 格式化消息
        formatted_msg = f"""
<div style="margin-bottom: 15px; border-left: 4px solid {color}; padding-left: 10px;">
    <div style="font-weight: bold; color: {color}; margin-bottom: 5px;">
        [{timestamp}] {title}
    </div>
    <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: 'Consolas', monospace; white-space: pre-wrap;">
{content}
    </div>
</div>
<hr style="border: none; border-top: 1px solid #eee; margin: 10px 0;">
"""

        # 添加到显示区
        cursor = self.display_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertHtml(formatted_msg)

        # 自动滚动到底部
        scrollbar = self.display_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_display(self):
        """清空显示区"""
        welcome_text = """欢迎使用智能体全流程展示界面！

请按照以下步骤开始使用：
1. 在配置区设置大模型的相关参数
2. 点击"保存配置"保存设置
3. 点击"测试大模型连接"验证配置
4. 选择要执行的智能体任务
5. 按步骤点击按钮，观察执行过程

功能说明：
- 任务1: 测试大模型连接，询问什么是MCP
- 任务2: 冒泡排序智能体，7个步骤完成排序算法实现
- 任务3: HTML文件制作，7个步骤完成网页制作
- 任务4: 预留功能，暂未实现

所有生成的文件将保存在 workspace_mcp 文件夹中。
"""
        self.display_text.setPlainText(welcome_text)
        self.current_task_label.setText("当前任务: 无")

    def closeEvent(self, event):
        """关闭事件"""
        logger.info("PySide6界面关闭")
        event.accept()
