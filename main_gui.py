#!/usr/bin/env python3
"""
智能体全流程展示界面 - PySide6版本启动脚本
"""
import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """主函数"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("智能体全流程展示界面")
        app.setApplicationVersion("2.0")
        
        # 设置应用样式
        app.setStyle('Fusion')
        
        # 导入主窗口
        from gui.main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("=" * 60)
        print("智能体全流程展示界面 - PySide6版本")
        print("=" * 60)
        print("界面已启动，请在窗口中进行操作")
        print("按 Ctrl+C 或关闭窗口退出程序")
        print("=" * 60)
        
        # 运行应用
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
