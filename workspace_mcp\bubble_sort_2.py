def bubble_sort(arr):
    """
    冒泡排序算法实现
    
    参数:
        arr: 待排序的列表
    
    返回:
        排序后的列表
    
    算法思路:
        通过重复遍历数组，比较相邻元素并交换位置，
        使较大的元素逐渐"冒泡"到数组末尾
    """
    # 获取数组长度
    n = len(arr)
    
    # 如果数组长度小于等于1，直接返回
    if n <= 1:
        return arr
    
    # 创建数组副本，避免修改原数组
    sorted_arr = arr.copy()
    
    # 外层循环控制排序轮数
    # 需要进行 n-1 轮排序
    for i in range(n - 1):
        # 标记本轮是否发生交换
        swapped = False
        
        # 内层循环进行相邻元素比较
        # 每轮后最大的元素会"冒泡"到末尾
        # 所以内层循环的范围可以逐渐减小
        for j in range(0, n - i - 1):
            # 如果前一个元素大于后一个元素，则交换
            if sorted_arr[j] > sorted_arr[j + 1]:
                sorted_arr[j], sorted_arr[j + 1] = sorted_arr[j + 1], sorted_arr[j]
                swapped = True
        
        # 如果本轮没有发生交换，说明数组已经有序，可以提前结束
        if not swapped:
            break
    
    return sorted_arr


def bubble_sort_simple(arr):
    """
    简化版冒泡排序（不包含提前终止优化）
    
    参数:
        arr: 待排序的列表
    
    返回:
        排序后的列表
    """
    n = len(arr)
    
    if n <= 1:
        return arr
    
    sorted_arr = arr.copy()
    
    # 外层循环控制排序轮数
    for i in range(n - 1):
        # 内层循环进行相邻元素比较
        for j in range(0, n - i - 1):
            # 如果前一个元素大于后一个元素，则交换
            if sorted_arr[j] > sorted_arr[j + 1]:
                sorted_arr[j], sorted_arr[j + 1] = sorted_arr[j + 1], sorted_arr[j]
    
    return sorted_arr


def print_array(arr, title="数组"):
    """
    打印数组的辅助函数
    
    参数:
        arr: 要打印的数组
        title: 打印标题
    """
    print(f"{title}: {arr}")


def main():
    """
    主函数，包含测试用例
    """
    print("=== 冒泡排序测试 ===\n")
    
    # 测试用例1：普通无序数组
    test_case1 = [64, 34, 25, 12]
    print("测试用例1：普通无序数组")
    print_array(test_case1, "原数组")
    result1 = bubble_sort(test_case1)
    print_array(result1, "排序后")
    print()
    
    # 测试用例2：已经有序的数组
    test_case2 = [1, 2, 3, 4, 5]
    print("测试用例2：已经有序的数组")
    print_array(test_case2, "原数组")
    result2 = bubble_sort(test_case2)
    print_array(result2, "排序后")
    print()
    
    # 测试用例3：逆序数组
    test_case3 = [5, 4, 3, 2, 1]
    print("测试用例3：逆序数组")
    print_array(test_case3, "原数组")
    result3 = bubble_sort(test_case3)
    print_array(result3, "排序后")
    print()
    
    # 测试用例4：包含重复元素的数组
    test_case4 = [3, 1, 4, 1, 5, 9, 2, 6, 5]
    print("测试用例4：包含重复元素的数组")
    print_array(test_case4, "原数组")
    result4 = bubble_sort(test_case4)
    print_array(result4, "排序后")
    print()
    
    # 测试用例5：单个元素
    test_case5 = [42]
    print("测试用例5：单个元素")
    print_array(test_case5, "原数组")
    result5 = bubble_sort(test_case5)
    print_array(result5, "排序后")
    print()
    
    # 测试用例6：空数组
    test_case6 = []
    print("测试用例6：空数组")
    print_array(test_case6, "原数组")
    result6 = bubble_sort(test_case6)
    print_array(result6, "排序后")
    print()
    
    # 测试用例7：两个元素
    test_case7 = [8, 3]
    print("测试用例7：两个元素")
    print_array(test_case7, "原数组")
    result7 = bubble_sort(test_case7)
    print_array(result7, "排序后")
    print()
    
    # 演示排序过程
    print("=== 排序过程演示 ===")
    demo_arr = [64, 34, 25, 12]
    print_array(demo_arr, "初始数组")
    
    n = len(demo_arr)
    for i in range(n - 1):
        print(f"\n第{i + 1}轮排序:")
        swapped = False
        for j in range(0, n - i - 1):
            if demo_arr[j] > demo_arr[j + 1]:
                demo_arr[j], demo_arr[j + 1] = demo_arr[j + 1], demo_arr[j]
                swapped = True
                print(f"  交换 {demo_arr[j + 1]} 和 {demo_arr[j]} -> {demo_arr}")
        
        if not swapped:
            print("  本轮无交换，排序完成")
            break
        else:
            print(f"  第{i + 1}轮结果: {demo_arr}")
    
    print(f"\n最终排序结果: {demo_arr}")


if __name__ == "__main__":
    main()
