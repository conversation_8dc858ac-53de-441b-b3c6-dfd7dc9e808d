"""
展示管理器 - 增强展示区信息显示
"""
import inspect
import datetime
from utils.logger import logger


class DisplayManager:
    def __init__(self):
        self.display_callback = None
    
    def set_display_callback(self, callback):
        """设置显示回调函数"""
        self.display_callback = callback
    
    def log_function_execution(self, func_name, func_obj, args=None, kwargs=None, description=""):
        """记录函数执行信息"""
        if not self.display_callback:
            return
        
        try:
            # 获取函数源代码
            source_code = self._get_function_source(func_obj)
            
            # 构建详细信息
            details = f"""
=== 函数执行详情 ===

函数名称: {func_name}
函数描述: {description}

执行的Python代码:
{source_code}

函数作用:
{self._get_function_purpose(func_obj)}

执行参数:
{self._format_arguments(args, kwargs)}

执行时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            self.display_callback(f"执行函数: {func_name}", details, "info")
            
        except Exception as e:
            logger.error(f"记录函数执行信息失败: {e}")
    
    def log_step_execution(self, step_name, step_description, code_snippet="", purpose="", process="", result="", source_file="", line_range=""):
        """记录步骤执行信息"""
        if not self.display_callback:
            return

        # 自动获取调用者信息
        if not source_file or not line_range:
            caller_info = self._get_caller_info()
            if not source_file:
                source_file = caller_info['file']
            if not line_range:
                line_range = caller_info['line_range']

        details = f"""
=== 步骤执行详情 ===

步骤名称: {step_name}
步骤描述: {step_description}

源码位置:
文件路径: {source_file}
代码行数: {line_range}

执行的Python代码:
{code_snippet if code_snippet else "无特定代码片段"}

步骤作用:
{purpose if purpose else "执行智能体任务的一个步骤"}

执行过程:
{process if process else "正在执行中..."}

执行结果:
{result if result else "等待执行完成"}

执行时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

代码详细分析:
{self._analyze_code_snippet(code_snippet)}
"""

        self.display_callback(step_name, details, "info")
    
    def log_llm_interaction(self, prompt, response, model="", purpose=""):
        """记录大模型交互信息"""
        if not self.display_callback:
            return
        
        details = f"""
=== 大模型交互详情 ===

交互目的: {purpose}
使用模型: {model}

发送的提示 (Prompt):
{prompt}

大模型响应:
{response}

交互时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Python代码逻辑:
```python
# 调用大模型API
headers = {{"Authorization": f"Bearer {{api_key}}", "Content-Type": "application/json"}}
data = {{
    "model": "{model}",
    "messages": [{{"role": "user", "content": prompt}}],
    "temperature": 0.7,
    "max_tokens": 2000
}}
response = requests.post(base_url + "/chat/completions", headers=headers, json=data)
```
"""
        
        self.display_callback("大模型交互", details, "info")
    
    def log_tool_usage(self, tool_name, command, result="", purpose=""):
        """记录工具使用信息"""
        if not self.display_callback:
            return
        
        details = f"""
=== 工具使用详情 ===

工具名称: {tool_name}
使用目的: {purpose}

执行的命令/操作:
{command}

工具作用:
{self._get_tool_purpose(tool_name)}

执行结果:
{result if result else "正在执行中..."}

执行时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Python代码逻辑:
{self._get_tool_code_example(tool_name, command)}
"""
        
        self.display_callback(f"工具使用: {tool_name}", details, "info")
    
    def log_file_operation(self, operation, filename, content_preview="", purpose=""):
        """记录文件操作信息"""
        if not self.display_callback:
            return
        
        details = f"""
=== 文件操作详情 ===

操作类型: {operation}
文件路径: {filename}
操作目的: {purpose}

Python代码逻辑:
```python
# 文件操作代码
import os
os.makedirs(os.path.dirname(filename), exist_ok=True)
with open(filename, 'w', encoding='utf-8') as f:
    f.write(content)
```

文件内容预览:
{content_preview[:500] + "..." if len(content_preview) > 500 else content_preview}

操作时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        self.display_callback(f"文件操作: {operation}", details, "info")
    
    def _get_function_source(self, func_obj):
        """获取函数源代码"""
        try:
            return inspect.getsource(func_obj)
        except Exception:
            return "无法获取源代码"
    
    def _get_function_purpose(self, func_obj):
        """获取函数用途"""
        try:
            doc = inspect.getdoc(func_obj)
            return doc if doc else "无文档说明"
        except Exception:
            return "无法获取函数说明"
    
    def _format_arguments(self, args, kwargs):
        """格式化参数"""
        if not args and not kwargs:
            return "无参数"
        
        result = []
        if args:
            result.append(f"位置参数: {args}")
        if kwargs:
            result.append(f"关键字参数: {kwargs}")
        
        return "\n".join(result)
    
    def _get_tool_purpose(self, tool_name):
        """获取工具用途"""
        purposes = {
            "文件生成": "创建新文件并写入内容",
            "文件读取": "读取文件内容",
            "代码验证": "验证代码语法和结构",
            "目录创建": "创建文件夹目录",
            "配置保存": "保存配置到YAML文件",
            "配置加载": "从YAML文件加载配置"
        }
        return purposes.get(tool_name, "执行特定的工具操作")
    
    def _get_tool_code_example(self, tool_name, command):
        """获取工具代码示例"""
        examples = {
            "文件生成": f"""```python
# 生成文件
filename = "{command}"
with open(filename, 'w', encoding='utf-8') as f:
    f.write(content)
```""",
            "配置保存": """```python
# 保存配置
import yaml
with open('configs/config.yaml', 'w', encoding='utf-8') as f:
    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
```""",
            "代码验证": """```python
# 验证代码语法
try:
    compile(code_content, filename, 'exec')
    print("语法检查通过")
except SyntaxError as e:
    print(f"语法错误: {e}")
```"""
        }
        return examples.get(tool_name, f"# {tool_name}相关代码\n# {command}")

    def _get_caller_info(self):
        """获取调用者信息"""
        try:
            import traceback
            stack = traceback.extract_stack()

            # 找到调用者（跳过当前方法和log_step_execution）
            for frame in reversed(stack[:-2]):
                if 'agents/' in frame.filename and frame.filename.endswith('.py'):
                    return {
                        'file': frame.filename,
                        'line_range': f"第{frame.lineno}行附近",
                        'function': frame.name
                    }

            return {
                'file': "未知文件",
                'line_range': "未知行数",
                'function': "未知函数"
            }
        except Exception:
            return {
                'file': "获取失败",
                'line_range': "获取失败",
                'function': "获取失败"
            }

    def _analyze_code_snippet(self, code_snippet):
        """分析代码片段"""
        if not code_snippet or code_snippet == "无特定代码片段":
            return "无代码片段可分析"

        analysis = []

        # 分析代码类型
        if "llm_client.call_llm" in code_snippet:
            analysis.append("- 这是大模型API调用代码")
            analysis.append("- 作用：向大模型发送请求并获取响应")

        if "os.makedirs" in code_snippet:
            analysis.append("- 这是目录创建代码")
            analysis.append("- 作用：确保输出目录存在")

        if "with open" in code_snippet:
            analysis.append("- 这是文件操作代码")
            analysis.append("- 作用：读取或写入文件内容")

        if "compile(" in code_snippet:
            analysis.append("- 这是代码验证代码")
            analysis.append("- 作用：检查Python代码语法是否正确")

        if "yaml.dump" in code_snippet:
            analysis.append("- 这是配置保存代码")
            analysis.append("- 作用：将配置数据保存到YAML文件")

        if "requests.post" in code_snippet:
            analysis.append("- 这是HTTP请求代码")
            analysis.append("- 作用：向API服务器发送POST请求")

        # 分析变量和函数
        lines = code_snippet.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('def '):
                analysis.append(f"- 定义函数：{line}")
            elif '=' in line and not line.startswith('#'):
                var_name = line.split('=')[0].strip()
                analysis.append(f"- 设置变量：{var_name}")

        return '\n'.join(analysis) if analysis else "代码片段分析完成"

    def log_detailed_execution(self, step_name, file_path, line_start, line_end, code_content, purpose, process_detail, result_detail):
        """记录详细的执行信息"""
        if not self.display_callback:
            return

        details = f"""
=== 详细执行信息 ===

步骤名称: {step_name}

源码位置:
文件路径: {file_path}
代码行数: 第{line_start}行 到 第{line_end}行

完整源码:
{code_content}

代码作用:
{purpose}

执行过程详情:
{process_detail}

执行结果详情:
{result_detail}

代码分析:
{self._analyze_code_snippet(code_content)}

执行时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        self.display_callback(f"详细执行: {step_name}", details, "info")


# 全局显示管理器实例
display_manager = DisplayManager()
