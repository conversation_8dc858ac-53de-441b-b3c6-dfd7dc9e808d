#!/usr/bin/env python3
"""
智能体全流程展示界面启动脚本
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

if __name__ == '__main__':
    try:
        from app import app
        print("=" * 60)
        print("智能体全流程展示界面")
        print("=" * 60)
        print("启动中...")
        print(f"项目目录: {project_root}")
        print("访问地址: http://localhost:5000")
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
