#!/usr/bin/env python3
"""
测试冒泡排序智能体修复
验证step_4_consult_code_structure方法是否存在
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_bubble_sort_methods():
    """测试冒泡排序智能体方法"""
    print("测试冒泡排序智能体方法...")
    
    try:
        from agents.bubble_sort_agent import bubble_sort_agent
        
        # 检查所有步骤方法是否存在
        required_methods = [
            'step_1_make_plan',
            'step_2_consult_algorithm', 
            'step_3_analyze_response',
            'step_4_consult_code_structure',  # 这个方法之前缺失
            'step_5_generate_code_file',
            'step_6_verify_code',
            'step_7_task_summary'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if hasattr(bubble_sort_agent, method_name):
                print(f"✓ 方法存在: {method_name}")
            else:
                missing_methods.append(method_name)
                print(f"✗ 方法缺失: {method_name}")
        
        if not missing_methods:
            print("✓ 所有必需的方法都存在")
            return True
        else:
            print(f"✗ 缺失方法: {missing_methods}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_display_manager_enhancements():
    """测试显示管理器增强功能"""
    print("\n测试显示管理器增强功能...")
    
    try:
        from utils.display_manager import display_manager
        
        # 测试新增的方法
        enhanced_methods = [
            'log_detailed_execution',
            '_get_caller_info',
            '_analyze_code_snippet'
        ]
        
        missing_methods = []
        for method_name in enhanced_methods:
            if hasattr(display_manager, method_name):
                print(f"✓ 增强方法存在: {method_name}")
            else:
                missing_methods.append(method_name)
                print(f"✗ 增强方法缺失: {method_name}")
        
        # 测试回调设置
        def test_callback(title, content, msg_type):
            print(f"回调测试: {title[:20]}... - {msg_type}")
        
        display_manager.set_display_callback(test_callback)
        
        # 测试详细执行记录
        display_manager.log_detailed_execution(
            "测试步骤",
            "test_file.py", 
            1, 10,
            "def test(): pass",
            "测试目的",
            "测试过程",
            "测试结果"
        )
        
        if not missing_methods:
            print("✓ 显示管理器增强功能正常")
            return True
        else:
            print(f"✗ 缺失增强方法: {missing_methods}")
            return False
            
    except Exception as e:
        print(f"✗ 显示管理器测试失败: {e}")
        return False

def test_step_execution():
    """测试步骤执行"""
    print("\n测试步骤执行...")
    
    try:
        from agents.bubble_sort_agent import bubble_sort_agent
        
        # 重置任务
        bubble_sort_agent.reset_task()
        print("✓ 任务重置成功")
        
        # 检查当前步骤
        step_info = bubble_sort_agent.get_current_step_info()
        print(f"✓ 当前步骤: {step_info['step_name']}")
        
        # 检查execute_current_step方法
        if hasattr(bubble_sort_agent, 'execute_current_step'):
            print("✓ execute_current_step方法存在")
            
            # 检查步骤方法列表
            if bubble_sort_agent.current_step < len(bubble_sort_agent.steps):
                print(f"✓ 可以执行步骤 {bubble_sort_agent.current_step + 1}")
                return True
            else:
                print("✗ 没有可执行的步骤")
                return False
        else:
            print("✗ execute_current_step方法不存在")
            return False
            
    except Exception as e:
        print(f"✗ 步骤执行测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("冒泡排序智能体修复验证测试")
    print("=" * 60)
    
    tests = [
        test_bubble_sort_methods,
        test_display_manager_enhancements,
        test_step_execution
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有修复验证通过！")
        print("✓ step_4_consult_code_structure方法已修复")
        print("✓ 显示管理器功能已增强")
        print("✓ 可以正常执行智能体任务")
        print("\n现在可以启动应用测试完整功能:")
        print("python main_gui.py")
    else:
        print("✗ 部分修复验证失败，请检查代码。")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
