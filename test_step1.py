#!/usr/bin/env python3
"""
测试文件操作智能体第一步
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_step1():
    """测试第一步执行"""
    try:
        print("测试文件操作智能体第一步...")

        # 导入文件操作智能体
        from agents.file_operation_agent import file_operation_agent

        # 设置一个简单的显示回调
        def simple_display(title, content, msg_type="info"):
            print(f"\n[{msg_type.upper()}] {title}")
            print("-" * 50)
            print(content)
            print("-" * 50)

        # 设置显示回调
        from utils.display_manager import display_manager
        display_manager.set_display_callback(simple_display)

        # 执行第一步
        print("执行第一步...")
        success, result = file_operation_agent._step1_start_mcp_server()

        if success:
            print("✓ 第一步执行成功！")
            print(f"结果: {result[:200]}...")
        else:
            print("✗ 第一步执行失败！")
            print(f"错误: {result}")

        return success

    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_step1()
    print(f"\n测试结果: {'成功' if success else '失败'}")