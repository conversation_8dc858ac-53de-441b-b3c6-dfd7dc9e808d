"""
冒泡排序智能体模块
将智能体完成冒泡排序任务的全流程拆分为多个步骤
"""
import os
from agents.llm_client import llm_client
from utils.logger import logger, logger_manager
from utils.display_manager import display_manager
import subprocess
import os

class BubbleSortAgent:
    def __init__(self):
        self.task_name = "冒泡排序任务"
        self.current_step = 0
        self.steps = [
            "制作任务计划",
            "咨询大模型-算法实现",
            "分析大模型返回",
            "咨询大模型-代码结构",
            "生成代码文件",
            "代码验证和测试",
            "任务完成总结"
        ]
        self.step_results = {}
        self.plan = ""
        self.algorithm_info = ""
        self.code_structure = ""
        self.generated_code = ""
    
    def reset_task(self):
        """重置任务状态"""
        self.current_step = 0
        self.step_results = {}
        self.plan = ""
        self.algorithm_info = ""
        self.code_structure = ""
        self.generated_code = ""
        logger.info("冒泡排序任务已重置")
    
    def get_current_step_info(self):
        """获取当前步骤信息"""
        if self.current_step < len(self.steps):
            return {
                'step_number': self.current_step + 1,
                'step_name': self.steps[self.current_step],
                'total_steps': len(self.steps),
                'is_completed': False
            }
        else:
            return {
                'step_number': len(self.steps),
                'step_name': '任务已完成',
                'total_steps': len(self.steps),
                'is_completed': True
            }
    
    def step_1_make_plan(self):
        """步骤1: 制作任务计划"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤1: 制作任务计划")
        
        try:
            # 记录详细执行信息
            display_manager.log_detailed_execution(
                "制作任务计划",
                "agents/bubble_sort_agent.py",
                61,
                115,
                """def step_1_make_plan(self):
    \"\"\"步骤1: 制作任务计划\"\"\"
    logger_manager.log_task_start(f"{self.task_name} - 步骤1: 制作任务计划")

    try:
        # 智能体分析任务并制作计划
        prompt = \"\"\"作为一个智能体，我需要完成冒泡排序算法的实现任务。请帮我制作一个详细的任务计划，包括：
1. 需要完成的主要步骤
2. 每个步骤的具体内容
3. 预期的输出结果
请用简洁明了的语言回答。\"\"\"

        # 记录大模型交互
        display_manager.log_llm_interaction(prompt, "等待大模型响应...", "Qwen3-Coder", "制作冒泡排序任务的详细执行计划")

        success, response = llm_client.call_llm(prompt)

        if success:
            self.plan = response
        else:
            # 如果大模型调用失败，使用备用计划
            self.plan = \"\"\"任务计划：实现冒泡排序算法...\"\"\"

        self.step_results[self.current_step] = {
            'step_name': self.steps[self.current_step],
            'result': self.plan,
            'success': True
        }

        logger_manager.log_step("制作计划", "任务计划制作完成")
        self.current_step += 1

        return True, self.plan""",
                "这个方法是冒泡排序智能体的第一个步骤，负责制作详细的任务执行计划。它会调用大模型来分析任务需求并生成执行计划。",
                "1. 构建提示词，要求大模型制作任务计划\n2. 调用llm_client.call_llm()方法发送请求\n3. 处理大模型返回的计划内容\n4. 如果调用失败，使用预设的备用计划\n5. 保存结果到step_results中\n6. 更新当前步骤计数器",
                f"成功生成任务计划，计划内容长度: {len(self.plan) if hasattr(self, 'plan') else 0} 字符"
            )

            # 用户prompt
            user_prompt = "写一个冒泡排序的python程序，存储在D:/workspace文件夹下"
            user_prompt = "写一个冒泡排序的python程序"

            # 智能体分析任务并制作计划
            # prompt = "作为一个智能体，我需要完成冒泡排序算法的实现任务。请帮我制作一个详细的任务计划，包括：1. 需要完成的主要步骤;2. 每个步骤的具体内容;3. 预期的输出结果;请用简洁明了的语言回答。"
            prompt = f"""我是一个智能体，我当前支持的功能为：写py文件，读取并优化python程序，直接优化用户提供的python代码，写html文件，读取并优化html程序，这5个功能。
我当前支持的工具有：PythonExecute。
用户交给我的任务是：{user_prompt}。请帮我制作一个详细的任务计划，包括：
1、用户的要求，属于上面5个任务中的哪个；
2、我需要使用哪些工具；
3、需要完成的主要步骤；
3、每个步骤的具体内容；
4、预期的输出结果；
5、我下一个需要向智能体发送的prompt；
请用简洁明了的语言回答。
"""

            # 记录大模型交互
            display_manager.log_llm_interaction(
                prompt,
                "等待大模型响应...",
                "Qwen3-Coder",
                "制作冒泡排序任务的详细执行计划"
            )

            success, response = llm_client.call_llm(prompt)

            if success:
                self.plan = response
                # 记录成功的大模型响应
                display_manager.log_llm_interaction(
                    prompt,
                    response,
                    "Qwen3-Coder",
                    "制作冒泡排序任务的详细执行计划"
                )
            else:
                # 如果大模型调用失败，使用备用计划
                self.plan = """
任务计划：实现冒泡排序算法
1. 分析冒泡排序算法原理
2. 设计Python代码结构
3. 实现核心排序逻辑
4. 添加输入输出处理
5. 添加测试用例
6. 验证代码正确性
                """.strip()
            
            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'result': self.plan,
                'success': True
            }
            
            logger_manager.log_step("制作计划", "任务计划制作完成")
            self.current_step += 1
            
            return True, self.plan
            
        except Exception as e:
            error_msg = f"制作计划失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def step_2_consult_algorithm(self):
        """步骤2: 咨询大模型-算法实现"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤2: 咨询大模型算法实现")
        
        try:
            prompt = """请详细解释冒泡排序算法的原理和实现步骤：
1. 算法的基本思想
2. 具体的实现步骤
3. 时间复杂度和空间复杂度
4. 优化方案
请用简洁明了的语言回答。"""
            
            success, response = llm_client.call_llm(prompt)
            
            if success:
                self.algorithm_info = response
                self.step_results[self.current_step] = {
                    'step_name': self.steps[self.current_step],
                    'prompt': prompt,
                    'result': response,
                    'success': True
                }
                
                logger_manager.log_step("咨询算法实现", "大模型返回算法详细信息")
                self.current_step += 1
                return True, response
            else:
                return False, response
                
        except Exception as e:
            error_msg = f"咨询算法实现失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def step_3_analyze_response(self):
        """步骤3: 分析大模型返回"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤3: 分析大模型返回")
        
        try:
            if not self.algorithm_info:
                return False, "没有算法信息可供分析"
            
            # 智能体分析大模型返回的信息
            analysis = f"""
分析结果：
- 大模型返回了详细的冒泡排序算法说明
- 包含了算法原理、实现步骤和复杂度分析
- 信息完整，可以用于下一步的代码结构设计
- 返回内容长度: {len(self.algorithm_info)} 字符
            """.strip()
            
            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'analysis': analysis,
                'algorithm_info_length': len(self.algorithm_info),
                'success': True
            }
            
            logger_manager.log_step("分析返回", "算法信息分析完成")
            self.current_step += 1
            
            return True, analysis
            
        except Exception as e:
            error_msg = f"分析返回失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def extract_python_code(self, response):
        start_marker = "```python"
        end_marker = "```"
        start_idx = response.find(start_marker)
        if start_idx == -1:
            return None
        start_idx += len(start_marker)  # 跳过起始标记
        end_idx = response.find(end_marker, start_idx)  # 从 start_idx 开始查找结束标记
        return response[start_idx:end_idx].strip() if end_idx != -1 else None

    def step_4_consult_code_structure(self):
        """步骤4: 咨询大模型-代码结构"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤4: 咨询大模型代码结构")

        try:
            # 记录步骤执行详情
            display_manager.log_step_execution(
                "咨询代码结构",
                "基于算法信息向大模型咨询具体的Python代码实现",
                """
# 步骤4执行代码
prompt = f'''基于以下冒泡排序算法信息，请设计一个完整的Python代码结构：
算法信息：{self.algorithm_info[:500]}...
请提供完整的Python代码实现...'''
success, response = llm_client.call_llm(prompt)
if success:
    self.code_structure = response
                """.strip(),
                "获取具体的Python代码实现，为生成文件做准备",
                "正在调用大模型获取代码结构..."
            )

            prompt = f"""基于以下冒泡排序算法信息，请设计一个完整的Python代码结构：

算法信息：
{self.algorithm_info[:500]}...

请提供：
1. 给出完整的Python代码实现
2. 包含主函数和测试用例
3. 添加适当的注释
4. 代码应该可以直接运行

请直接提供可执行的Python代码。"""

            # 记录大模型交互
            display_manager.log_llm_interaction(
                prompt,
                "等待大模型响应...",
                "Qwen3-Coder",
                "获取冒泡排序的完整Python代码实现"
            )

            success, response = llm_client.call_llm(prompt)
            # success, response = llm_client.call_llm_tools(prompt)

            if success:
                # self.code_structure = response
                self.code_structure = self.extract_python_code(response)
                # 记录成功的大模型响应
                display_manager.log_llm_interaction(
                    prompt,
                    response,
                    "Qwen3-Coder",
                    "获取冒泡排序的完整Python代码实现"
                )

                self.step_results[self.current_step] = {
                    'step_name': self.steps[self.current_step],
                    'prompt': prompt,
                    'result': response,
                    'success': True
                }

                logger_manager.log_step("咨询代码结构", "大模型返回代码结构")
                self.current_step += 1
                return True, response
            else:
                return False, response

        except Exception as e:
            error_msg = f"咨询代码结构失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def step_5_generate_code_file(self):
        """步骤5: 生成代码文件"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤5: 生成代码文件")

        try:
            if not self.code_structure:
                return False, "没有代码结构可供生成文件"

            # 确保输出目录存在
            os.makedirs('workspace_mcp', exist_ok=True)

            # 生成文件名
            filename = 'workspace_mcp/bubble_sort.py'

            # 写入代码文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.code_structure)

            self.generated_code = self.code_structure

            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'filename': filename,
                'code_length': len(self.code_structure),
                'success': True
            }

            logger_manager.log_tool_call("文件生成", f"写入文件: {filename}", f"代码长度: {len(self.code_structure)}")
            logger_manager.log_step("生成代码文件", f"文件已保存: {filename}")
            self.current_step += 1

            return True, f"代码文件已生成: {filename}\n代码长度: {len(self.code_structure)} 字符"

        except Exception as e:
            error_msg = f"生成代码文件失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def execute_python_script(self, script_path: str, args: list = None) -> dict:
        # 验证路径合法性（防止路径遍历攻击）
        # if not os.path.abspath(script_path).startswith("/safe/directory"):
        #     return {"error": "非法路径"}

        try:
            # 构建命令：python <脚本路径> <参数>
            cmd = ["python", script_path] + (args if args else [])
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding="utf-8",  # 显式指定编码
                timeout=30,  # 设置超时防止阻塞
                check=True  # 返回非0状态码时抛出异常
            )
            print(f"execute_python result:   {result}")
            print(f"execute_python result.stdout  : {result.stdout}")
            print(f"execute_python result.stderr:   {result.stderr}")
            print(f"execute_python result.returncode:   {result.returncode}")
            # return {
            #     "stdout": result.stdout,
            #     "stderr": result.stderr,
            #     "returncode": result.returncode
            # }
            return {
                "stdout": result.stdout if result.stdout is not None else "",
                "stderr": result.stderr if result.stderr is not None else "",
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {"error": "执行超时"}
        except FileNotFoundError:
            return {"error": f"文件不存在: {script_path}"}
        except subprocess.CalledProcessError as e:
            return {"error": f"执行失败（代码{e.returncode}）", "stderr": e.stderr}


    def step_6_verify_code(self):
        """步骤6: 代码验证和测试"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤6: 代码验证和测试")

        try:
            if not self.generated_code:
                return False, "没有生成的代码可供验证"

            # 尝试执行代码进行验证
            filename = 'workspace_mcp/bubble_sort.py'

            if not os.path.exists(filename):
                return False, f"代码文件不存在: {filename}"

            # 简单的语法检查
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    code_content = f.read()

                # 编译检查语法
                compile(code_content, filename, 'exec')
                syntax_check = "语法检查通过"

            except SyntaxError as e:
                syntax_check = f"语法错误: {str(e)}"

            verification_result = f"""
代码验证结果：
- 文件路径: {filename}
- 文件大小: {len(self.generated_code)} 字符
- 语法检查: {syntax_check}
- 代码包含冒泡排序实现: {'是' if 'bubble' in self.generated_code.lower() or 'sort' in self.generated_code.lower() else '否'}
            """.strip()

            prompt = f"""读取D:/proj/30MCP/MCPAgent/MCP_Agent/workspace_mcp/bubble_sort.py，提供使用PythonExecute工具运行此程序的完整命令"""

            success, arguments = llm_client.call_llm_tools(prompt)

            if success:
                # self.code_structure = response
                # 记录成功的大模型响应
                display_manager.log_llm_interaction(
                    prompt,
                    arguments,
                    "Qwen3-Coder",
                    "获取PythonExecute工具运行程序的完整命令实现"
                )

                script_path = arguments["script_path"]  # 必选参数
                cli_args = arguments.get("arguments", [])  # 可选参数（默认为空列表）

                exec_result = self.execute_python_script(script_path, cli_args)

                if "error" in exec_result:
                    response_text = f"❌ 执行失败: {exec_result['error']}"
                    if exec_result.get("stderr"):
                        response_text += f"\n错误详情: {exec_result['stderr'][:500]}..."  # 截断错误输出
                else:
                    output = exec_result["stdout"]
                    if output is None:  # 显式处理 None
                        output = ""
                    else:
                        # 截断长文本
                        output = output[:2000] + "..." if len(output) > 2000 else output
                    response_text = f"✅ 执行成功！\n输出结果:\n{output}"

                print(f"response_text2222222：  {response_text}")

                self.step_results[self.current_step] = {
                    'step_name': self.steps[self.current_step],
                    'prompt': prompt,
                    'result': response_text,
                    'success': True
                }

                logger_manager.log_step("咨询代码结构", "大模型返回代码结构")


            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'filename': filename,
                'syntax_check': syntax_check,
                'verification_result': verification_result,
                'success': True
            }

            logger_manager.log_step("代码验证", verification_result)
            self.current_step += 1

            return True, verification_result

        except Exception as e:
            error_msg = f"代码验证失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def step_7_task_summary(self):
        """步骤7: 任务完成总结"""
        logger_manager.log_task_start(f"{self.task_name} - 步骤7: 任务完成总结")

        try:
            # 生成任务总结
            summary = f"""
=== 冒泡排序任务完成总结 ===

任务执行步骤：
"""

            for i, (step_idx, result) in enumerate(self.step_results.items()):
                summary += f"{i+1}. {result['step_name']}: {'成功' if result['success'] else '失败'}\n"

            summary += f"""
最终成果：
- 生成的代码文件: workspace_mcp/bubble_sort.py
- 代码长度: {len(self.generated_code)} 字符
- 任务状态: 已完成

智能体工作流程：
1. 制作详细的任务计划
2. 咨询大模型获取算法原理
3. 分析大模型返回的信息
4. 再次咨询大模型获取代码实现
5. 生成可执行的Python代码文件
6. 验证代码语法和结构
7. 完成任务总结

本次演示展示了智能体如何：
- 分步骤执行复杂任务
- 与大模型进行多轮交互
- 分析和处理大模型返回
- 调用工具生成文件
- 验证工作成果
            """.strip()

            self.step_results[self.current_step] = {
                'step_name': self.steps[self.current_step],
                'summary': summary,
                'total_steps_completed': len(self.step_results),
                'success': True
            }

            logger_manager.log_task_end(self.task_name, True)
            logger_manager.log_step("任务总结", "冒泡排序任务全流程完成")
            self.current_step += 1

            return True, summary

        except Exception as e:
            error_msg = f"任务总结失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def execute_current_step(self):
        """执行当前步骤"""
        if self.current_step >= len(self.steps):
            return False, "所有步骤已完成"

        step_methods = [
            self.step_1_make_plan,
            self.step_2_consult_algorithm,
            self.step_3_analyze_response,
            self.step_4_consult_code_structure,
            self.step_5_generate_code_file,
            self.step_6_verify_code,
            self.step_7_task_summary
        ]

        try:
            return step_methods[self.current_step]()
        except Exception as e:
            error_msg = f"执行步骤失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_all_results(self):
        """获取所有步骤的结果"""
        return self.step_results


# 全局冒泡排序智能体实例
bubble_sort_agent = BubbleSortAgent()
