#!/usr/bin/env python3
"""
PySide6版本测试脚本
验证所有模块是否可以正常导入和初始化
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_pyside6_import():
    """测试PySide6导入"""
    print("测试PySide6导入...")
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow
        from PySide6.QtCore import Qt, QThread, Signal
        from PySide6.QtGui import QFont
        print("✓ PySide6模块导入成功")
        return True
        
    except ImportError as e:
        print(f"✗ PySide6导入失败: {e}")
        print("请安装PySide6: pip install PySide6")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    
    try:
        from gui.main_window import MainWindow, TaskWorker
        print("✓ GUI模块导入成功")
        return True
        
    except ImportError as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False

def test_enhanced_modules():
    """测试增强模块"""
    print("\n测试增强模块...")
    
    try:
        from utils.display_manager import display_manager
        print("✓ 显示管理器导入成功")
        
        # 测试显示管理器功能
        def dummy_callback(title, content, msg_type):
            print(f"回调测试: {title} - {msg_type}")
        
        display_manager.set_display_callback(dummy_callback)
        display_manager.log_step_execution("测试步骤", "测试描述", "test_code", "测试目的")
        print("✓ 显示管理器功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 增强模块测试失败: {e}")
        return False

def test_config_updates():
    """测试配置更新"""
    print("\n测试配置更新...")
    
    try:
        from utils.config_manager import config_manager
        
        # 测试默认配置
        config = config_manager.get_llm_config()
        expected_values = {
            'base_url': 'http://10.227.14.2:12434/v1',
            'api_key': 'EMPTY',
            'model': 'Qwen3-Coder'
        }
        
        for key, expected in expected_values.items():
            if config.get(key) == expected:
                print(f"✓ 配置项 {key} 正确: {expected}")
            else:
                print(f"✗ 配置项 {key} 错误: 期望 {expected}, 实际 {config.get(key)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_file_paths():
    """测试文件路径"""
    print("\n测试文件路径...")
    
    try:
        from agents.bubble_sort_agent import bubble_sort_agent
        from agents.html_agent import html_agent
        
        # 检查输出路径是否正确
        if 'workspace_mcp' in str(bubble_sort_agent.__dict__):
            print("✓ 冒泡排序智能体使用正确的输出路径")
        
        if 'workspace_mcp' in str(html_agent.__dict__):
            print("✓ HTML智能体使用正确的输出路径")
        
        # 确保目录存在
        os.makedirs('workspace_mcp', exist_ok=True)
        print("✓ workspace_mcp目录已创建")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件路径测试失败: {e}")
        return False

def test_llm_question():
    """测试大模型问题"""
    print("\n测试大模型问题...")
    
    try:
        from agents.llm_client import llm_client
        
        # 检查测试连接的问题是否正确
        print("✓ 大模型客户端可用")
        print("注意: 测试连接现在会询问'什么是MCP'")
        
        return True
        
    except Exception as e:
        print(f"✗ 大模型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("智能体全流程展示界面 - PySide6版本测试")
    print("=" * 60)
    
    tests = [
        test_pyside6_import,
        test_gui_import,
        test_enhanced_modules,
        test_config_updates,
        test_file_paths,
        test_llm_question
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！PySide6版本设置正确。")
        print("\n可以运行以下命令启动应用:")
        print("python main_gui.py")
    else:
        print("✗ 部分测试失败，请检查项目设置。")
        if passed < 2:
            print("\n建议先安装依赖:")
            print("pip install -r requirements.txt")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
