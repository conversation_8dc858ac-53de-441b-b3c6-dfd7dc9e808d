```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历 - 张三</title>
    <meta name="description" content="张三的个人简历，包含个人信息、技能、工作经验和教育背景">
    <style>
        /* CSS变量定义 */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --text-color: #333;
            --bg-color: #f8f9fa;
            --border-color: #ddd;
            --font-main: 'Microsoft YaHei', Arial, sans-serif;
        }

        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-main);
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
        }

        /* 容器布局 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 头部样式 */
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo h1 {
            font-size: 1.8rem;
        }

        nav ul {
            display: flex;
            list-style: none;
        }

        nav ul li {
            margin-left: 2rem;
        }

        nav ul li a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        nav ul li a:hover {
            color: var(--secondary-color);
        }

        /* 主体内容 */
        main {
            padding: 2rem 0;
        }

        .profile-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .personal-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .info-item {
            margin-bottom: 1rem;
        }

        .info-label {
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.2rem;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }

        .skill-item {
            background: var(--secondary-color);
            color: white;
            padding: 0.8rem;
            border-radius: 4px;
            text-align: center;
        }

        .experience-item, .education-item {
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .experience-item:last-child, .education-item:last-child {
            border-bottom: none;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .item-title {
            font-weight: bold;
            color: var(--primary-color);
        }

        .item-subtitle {
            color: var(--secondary-color);
        }

        .item-date {
            color: #777;
            font-size: 0.9rem;
        }

        .contact-form {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: var(--font-main);
        }

        .submit-btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }

        .submit-btn:hover {
            background: #2980b9;
        }

        /* 底部样式 */
        footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 2rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            nav ul {
                margin-top: 1rem;
            }

            nav ul li {
                margin: 0 0.5rem;
            }

            .personal-info {
                grid-template-columns: 1fr;
            }

            .skills-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 10px;
            }

            nav ul {
                flex-wrap: wrap;
                justify-content: center;
            }

            nav ul li {
                margin: 0.2rem;
            }

            .skills-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header id="header" class="header">
        <div class="container header-content">
            <div class="logo">
                <h1>张三的简历</h1>
            </div>
            <nav class="navigation">
                <ul>
                    <li><a href="#profile">个人简介</a></li>
                    <li><a href="#skills">技能</a></li>
                    <li><a href="#experience">工作经历</a></li>
                    <li><a href="#education">教育背景</a></li>
                    <li><a href="#contact">联系方式</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- 主体内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 个人简介 -->
            <section id="profile" class="profile-section">
                <h2 class="section-title">个人简介</h2>
                <div class="personal-info">
                    <div class="info-column">
                        <div class="info-item">
                            <div class="info-label">姓名</div>
                            <div>张三</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">性别</div>
                            <div>男</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">年龄</div>
                            <div>28岁</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">联系电话</div>
                            <div>138-0000-0000</div>
                        </div>
                    </div>
                    <div class="info-column">
                        <div class="info-item">
                            <div class="info-label">邮箱地址</div>
                            <div><EMAIL></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">求职意向</div>
                            <div>前端开发工程师</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">工作年限</div>
                            <div>5年</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">现居地</div>
                            <div>北京市朝阳区</div>
                        </div>
                    </div>
                </div>
                <p style="margin-top: 1rem;">
                    热爱前端开发，具备扎实的HTML/CSS/JavaScript基础，熟悉Vue.js和React框架，
                    具有良好的代码规范意识和团队协作