"""
文件操作智能体模块
将智能体完成文件操作任务的全流程拆分为多个步骤
"""
import os
from agents.llm_client import llm_client
from utils.logger import logger, logger_manager
from utils.display_manager import display_manager
# from mcp.mcp_file_server import mcp_file_server  # 暂时注释掉，使用直接文件操作


class FileOperationAgent:
    def __init__(self):
        self.task_name = "文件操作任务"
        self.current_step = 0
        self.steps = [
            "启动MCP文件服务",
            "分析文件操作需求",
            "制定操作计划",
            "执行文件读取操作",
            "执行文件写入操作",
            "执行目录管理操作",
            "任务完成总结"
        ]
        self.step_results = {}
        self.mcp_server_status = ""
        self.operation_requirements = ""
        self.operation_plan = ""
        self.read_results = ""
        self.write_results = ""
        self.directory_results = ""

    def reset_task(self):
        """重置任务状态"""
        self.current_step = 0
        self.step_results = {}
        self.mcp_server_status = ""
        self.operation_requirements = ""
        self.operation_plan = ""
        self.read_results = ""
        self.write_results = ""
        self.directory_results = ""
        logger.info("文件操作任务已重置")

    def get_current_step_info(self):
        """获取当前步骤信息"""
        if self.current_step < len(self.steps):
            return {
                'step_number': self.current_step + 1,
                'step_name': self.steps[self.current_step],
                'total_steps': len(self.steps),
                'is_completed': False
            }
        else:
            return {
                'step_number': len(self.steps),
                'step_name': "任务已完成",
                'total_steps': len(self.steps),
                'is_completed': True
            }

    def execute_current_step(self):
        """执行当前步骤"""
        if self.current_step >= len(self.steps):
            return False, "所有步骤已完成"

        step_name = self.steps[self.current_step]
        logger.info(f"开始执行步骤 {self.current_step + 1}: {step_name}")

        try:
            if self.current_step == 0:
                success, result = self._step1_start_mcp_server()
            elif self.current_step == 1:
                success, result = self._step2_analyze_requirements()
            elif self.current_step == 2:
                success, result = self._step3_make_plan()
            elif self.current_step == 3:
                success, result = self._step4_execute_read_operations()
            elif self.current_step == 4:
                success, result = self._step5_execute_write_operations()
            elif self.current_step == 5:
                success, result = self._step6_execute_directory_operations()
            elif self.current_step == 6:
                success, result = self._step7_task_summary()
            else:
                return False, "未知步骤"

            if success:
                self.step_results[self.current_step] = result
                self.current_step += 1
                logger.info(f"步骤 {self.current_step}: {step_name} 执行成功")
            else:
                logger.error(f"步骤 {self.current_step + 1}: {step_name} 执行失败: {result}")

            return success, result

        except Exception as e:
            error_msg = f"执行步骤 {self.current_step + 1} 时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def _step1_start_mcp_server(self):
        """步骤1: 启动MCP文件服务"""
        try:
            display_manager.display("步骤1: 启动MCP文件服务", "正在启动MCP文件操作服务器...")

            # 模拟启动MCP服务器（实际使用直接文件操作）
            import time
            time.sleep(1)  # 模拟启动时间

            # 模拟服务器状态
            self.mcp_server_status = "MCP服务器已启动 - 主机: localhost, 端口: 3001"

            display_result = f"""MCP文件服务器启动成功！

服务器信息:
- 名称: File Operation Server
- 主机: localhost
- 端口: 3001
- 工作目录: {os.getcwd()}
- 运行状态: 运行中

可用的文件操作功能:
- 读取文件内容
- 写入文件内容
- 列出目录内容
- 创建目录
- 删除文件或目录
- 复制文件或目录
- 移动/重命名文件或目录
- 获取文件信息
- 获取当前工作目录

注意: 当前使用直接文件系统操作模拟MCP协议功能"""

            display_manager.display("步骤1: 启动MCP文件服务", display_result)
            return True, display_result

        except Exception as e:
            error_msg = f"启动MCP服务器时发生错误: {str(e)}"
            display_manager.display("步骤1: 启动MCP文件服务", error_msg)
            return False, error_msg

    def _step2_analyze_requirements(self):
        """步骤2: 分析文件操作需求"""
        try:
            display_manager.display("步骤2: 分析文件操作需求", "正在分析文件操作需求...")

            # 构建提示词
            prompt = """作为文件操作专家，请分析以下文件操作场景的需求：

场景描述：
我们需要演示一个完整的文件操作流程，包括：
1. 创建一个测试目录
2. 在目录中创建几个示例文件
3. 读取文件内容
4. 修改文件内容
5. 复制和移动文件
6. 清理测试文件

请分析这个场景的具体需求，并说明每个操作的目的和预期结果。
回答要求：
- 详细分析每个操作的需求
- 说明操作的顺序和依赖关系
- 预期的结果和可能的问题
- 建议的最佳实践

请用中文回答，内容要详细且实用。"""

            # 调用大模型
            success, response = llm_client.call_llm(prompt)

            if success:
                self.operation_requirements = response

                display_result = f"""文件操作需求分析完成！

{response}

需求分析已保存，将用于后续步骤的操作计划制定。"""

                display_manager.display("步骤2: 分析文件操作需求", display_result)
                return True, display_result
            else:
                error_msg = f"大模型调用失败: {response}"
                display_manager.display("步骤2: 分析文件操作需求", error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"分析需求时发生错误: {str(e)}"
            display_manager.display("步骤2: 分析文件操作需求", error_msg)
            return False, error_msg

    def _step3_make_plan(self):
        """步骤3: 制定操作计划"""
        try:
            display_manager.display("步骤3: 制定操作计划", "正在制定详细的文件操作计划...")

            # 构建提示词
            prompt = f"""基于以下需求分析，请制定一个详细的文件操作执行计划：

需求分析：
{self.operation_requirements}

请制定一个具体的操作计划，包括：
1. 具体的文件路径和名称
2. 每个操作的详细步骤
3. 操作的顺序和时机
4. 预期的结果验证方法

计划要求：
- 使用 workspace_mcp 目录作为工作目录
- 创建有意义的测试文件和目录
- 包含完整的操作流程
- 考虑错误处理和回滚

请用中文回答，提供可执行的详细计划。"""

            # 调用大模型
            success, response = llm_client.call_llm(prompt)

            if success:
                self.operation_plan = response

                display_result = f"""文件操作计划制定完成！

{response}

操作计划已保存，将按此计划执行后续的文件操作步骤。"""

                display_manager.display("步骤3: 制定操作计划", display_result)
                return True, display_result
            else:
                error_msg = f"大模型调用失败: {response}"
                display_manager.display("步骤3: 制定操作计划", error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"制定计划时发生错误: {str(e)}"
            display_manager.display("步骤3: 制定操作计划", error_msg)
            return False, error_msg

    def _step4_execute_read_operations(self):
        """步骤4: 执行文件读取操作"""
        try:
            display_manager.display("步骤4: 执行文件读取操作", "正在执行文件读取操作...")

            operations_log = []

            # 1. 获取当前工作目录
            try:
                import os
                current_dir = os.getcwd()
                operations_log.append(f"当前工作目录: {current_dir}")
            except Exception as e:
                operations_log.append(f"获取当前目录失败: {str(e)}")

            # 2. 列出workspace_mcp目录内容
            try:
                import os
                workspace_path = os.path.join(current_dir, "workspace_mcp")
                if os.path.exists(workspace_path):
                    items = os.listdir(workspace_path)
                    operations_log.append(f"workspace_mcp目录内容: {len(items)} 个项目")
                    for item in items[:5]:  # 只显示前5个
                        item_path = os.path.join(workspace_path, item)
                        item_type = "directory" if os.path.isdir(item_path) else "file"
                        operations_log.append(f"  - {item} ({item_type})")
                else:
                    operations_log.append("workspace_mcp目录不存在")
            except Exception as e:
                operations_log.append(f"列出目录失败: {str(e)}")

            # 3. 尝试读取一些现有文件
            test_files = ["workspace_mcp/bubble_sort.py", "workspace_mcp/resume.html"]
            for file_path in test_files:
                try:
                    if os.path.exists(file_path):
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()
                        content_preview = content[:200] + "..." if len(content) > 200 else content
                        operations_log.append(f"成功读取文件 {file_path} ({len(content)} 字节)")
                        operations_log.append(f"内容预览: {content_preview}")
                    else:
                        operations_log.append(f"文件不存在: {file_path}")
                except Exception as e:
                    operations_log.append(f"读取文件 {file_path} 失败: {str(e)}")

            self.read_results = "\n".join(operations_log)

            display_result = f"""文件读取操作完成！

执行的操作：
{self.read_results}

读取操作已完成，获取了目录结构和文件内容信息。"""

            display_manager.display("步骤4: 执行文件读取操作", display_result)
            return True, display_result

        except Exception as e:
            error_msg = f"执行读取操作时发生错误: {str(e)}"
            display_manager.display("步骤4: 执行文件读取操作", error_msg)
            return False, error_msg

    def _step5_execute_write_operations(self):
        """步骤5: 执行文件写入操作"""
        try:
            display_manager.display("步骤5: 执行文件写入操作", "正在执行文件写入操作...")

            operations_log = []

            # 1. 创建测试目录
            try:
                test_dir = "workspace_mcp/file_operation_test"
                os.makedirs(test_dir, exist_ok=True)
                operations_log.append("✓ 成功创建测试目录: workspace_mcp/file_operation_test")
            except Exception as e:
                operations_log.append(f"✗ 创建目录失败: {str(e)}")

            # 2. 创建测试文件1
            test_content1 = """# 文件操作测试文件1
这是一个由MCP文件操作智能体创建的测试文件。

创建时间: 2025-09-02
用途: 演示文件写入功能

内容包括:
- 文本内容
- 中文字符
- 特殊符号: !@#$%^&*()
"""

            try:
                file_path1 = "workspace_mcp/file_operation_test/test_file1.txt"
                with open(file_path1, "w", encoding="utf-8") as f:
                    f.write(test_content1)
                operations_log.append(f"✓ 成功创建文件: test_file1.txt ({len(test_content1)} 字节)")
            except Exception as e:
                operations_log.append(f"✗ 创建文件失败: {str(e)}")

            # 3. 创建测试文件2 (Python代码)
            test_content2 = '''"""
MCP文件操作测试脚本
由文件操作智能体自动生成
"""

def hello_mcp():
    """MCP测试函数"""
    print("Hello from MCP File Operation Agent!")
    print("这是一个测试Python文件")
    return "MCP文件操作成功"

if __name__ == "__main__":
    result = hello_mcp()
    print(f"执行结果: {result}")
'''

            try:
                file_path2 = "workspace_mcp/file_operation_test/test_script.py"
                with open(file_path2, "w", encoding="utf-8") as f:
                    f.write(test_content2)
                operations_log.append(f"✓ 成功创建Python文件: test_script.py ({len(test_content2)} 字节)")
            except Exception as e:
                operations_log.append(f"✗ 创建Python文件失败: {str(e)}")

            self.write_results = "\n".join(operations_log)

            display_result = f"""文件写入操作完成！

执行的操作：
{self.write_results}

写入操作已完成，创建了测试目录和多个测试文件。"""

            display_manager.display("步骤5: 执行文件写入操作", display_result)
            return True, display_result

        except Exception as e:
            error_msg = f"执行写入操作时发生错误: {str(e)}"
            display_manager.display("步骤5: 执行文件写入操作", error_msg)
            return False, error_msg

    def _step6_execute_directory_operations(self):
        """步骤6: 执行目录管理操作"""
        try:
            display_manager.display("步骤6: 执行目录管理操作", "正在执行目录管理操作...")

            operations_log = []

            # 1. 复制文件
            try:
                import shutil
                src_file = "workspace_mcp/file_operation_test/test_file1.txt"
                dst_file = "workspace_mcp/file_operation_test/test_file1_copy.txt"
                shutil.copy2(src_file, dst_file)
                operations_log.append("✓ 成功复制文件: test_file1.txt -> test_file1_copy.txt")
            except Exception as e:
                operations_log.append(f"✗ 复制文件失败: {str(e)}")

            # 2. 创建子目录
            try:
                subdir_path = "workspace_mcp/file_operation_test/subdir"
                os.makedirs(subdir_path, exist_ok=True)
                operations_log.append("✓ 成功创建子目录: subdir")
            except Exception as e:
                operations_log.append(f"✗ 创建子目录失败: {str(e)}")

            # 3. 移动文件到子目录
            try:
                src_file = "workspace_mcp/file_operation_test/test_file1_copy.txt"
                dst_file = "workspace_mcp/file_operation_test/subdir/moved_file.txt"
                shutil.move(src_file, dst_file)
                operations_log.append("✓ 成功移动文件到子目录: moved_file.txt")
            except Exception as e:
                operations_log.append(f"✗ 移动文件失败: {str(e)}")

            # 4. 获取文件信息
            try:
                file_path = "workspace_mcp/file_operation_test/test_script.py"
                if os.path.exists(file_path):
                    stat_info = os.stat(file_path)
                    operations_log.append("✓ 获取文件信息成功:")
                    operations_log.append(f"  文件大小: {stat_info.st_size} 字节")
                    operations_log.append("  文件类型: file")
                else:
                    operations_log.append("✗ 文件不存在")
            except Exception as e:
                operations_log.append(f"✗ 获取文件信息失败: {str(e)}")

            # 5. 列出最终的目录结构
            try:
                test_dir = "workspace_mcp/file_operation_test"
                if os.path.exists(test_dir):
                    operations_log.append("✓ 最终目录结构:")
                    for item in os.listdir(test_dir):
                        item_path = os.path.join(test_dir, item)
                        item_type = "directory" if os.path.isdir(item_path) else "file"
                        operations_log.append(f"  - {item} ({item_type})")
                else:
                    operations_log.append("✗ 测试目录不存在")
            except Exception as e:
                operations_log.append(f"✗ 列出目录失败: {str(e)}")

            self.directory_results = "\n".join(operations_log)

            display_result = f"""目录管理操作完成！

执行的操作：
{self.directory_results}

目录管理操作已完成，演示了文件复制、移动和目录创建等功能。"""

            display_manager.display("步骤6: 执行目录管理操作", display_result)
            return True, display_result

        except Exception as e:
            error_msg = f"执行目录操作时发生错误: {str(e)}"
            display_manager.display("步骤6: 执行目录管理操作", error_msg)
            return False, error_msg

    def _step7_task_summary(self):
        """步骤7: 任务完成总结"""
        try:
            display_manager.display("步骤7: 任务完成总结", "正在生成任务总结...")

            # 构建总结提示词
            prompt = f"""请基于以下文件操作执行结果，生成一个详细的任务完成总结：

MCP服务器状态：
{self.mcp_server_status}

需求分析结果：
{self.operation_requirements[:500]}...

操作计划：
{self.operation_plan[:500]}...

读取操作结果：
{self.read_results}

写入操作结果：
{self.write_results}

目录管理操作结果：
{self.directory_results}

请生成一个总结报告，包括：
1. 任务执行概况
2. 各步骤完成情况
3. 创建的文件和目录
4. 操作成功率统计
5. 遇到的问题和解决方案
6. MCP协议使用效果评价
7. 改进建议

请用中文回答，内容要专业且详细。"""

            # 调用大模型生成总结
            success, response = llm_client.call_llm(prompt)

            if success:
                display_result = f"""文件操作任务完成总结

{response}

🎉 恭喜！文件操作智能体任务已全部完成！

本次任务成功演示了：
✓ MCP协议的文件操作服务
✓ 智能体的多步骤任务执行
✓ 文件系统的完整操作流程
✓ 大模型与MCP服务的集成

所有生成的测试文件保存在 workspace_mcp/file_operation_test 目录中。"""

                display_manager.display("步骤7: 任务完成总结", display_result)
                return True, display_result
            else:
                error_msg = f"生成总结失败: {response}"
                display_manager.display("步骤7: 任务完成总结", error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"生成总结时发生错误: {str(e)}"
            display_manager.display("步骤7: 任务完成总结", error_msg)
            return False, error_msg


# 创建全局实例
file_operation_agent = FileOperationAgent()