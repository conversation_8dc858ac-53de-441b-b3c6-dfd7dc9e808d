#!/usr/bin/env python3
"""
项目设置测试脚本
验证所有模块是否可以正常导入和初始化
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试工具模块
        from utils.logger import logger, logger_manager
        print("✓ 日志模块导入成功")
        
        from utils.config_manager import config_manager
        print("✓ 配置管理模块导入成功")
        
        # 测试智能体模块
        from agents.llm_client import llm_client
        print("✓ LLM客户端模块导入成功")
        
        from agents.bubble_sort_agent import bubble_sort_agent
        print("✓ 冒泡排序智能体模块导入成功")
        
        from agents.html_agent import html_agent
        print("✓ HTML智能体模块导入成功")
        
        # 测试Flask应用
        from app import app
        print("✓ Flask应用导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_directories():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_dirs = ['configs', 'logs', 'output', 'static', 'templates', 'utils', 'agents']
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✓ 目录存在: {dir_name}")
        else:
            print(f"✗ 目录缺失: {dir_name}")
            os.makedirs(dir_name, exist_ok=True)
            print(f"  已创建目录: {dir_name}")

def test_config():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from utils.config_manager import config_manager
        
        # 测试配置加载
        config = config_manager.get_config()
        print("✓ 配置加载成功")
        
        # 测试LLM配置
        llm_config = config_manager.get_llm_config()
        print("✓ LLM配置获取成功")
        print(f"  当前模型: {llm_config.get('model', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_logger():
    """测试日志系统"""
    print("\n测试日志系统...")
    
    try:
        from utils.logger import logger, logger_manager
        
        # 测试基本日志
        logger.info("测试日志消息")
        print("✓ 基本日志功能正常")
        
        # 测试任务日志
        logger_manager.log_task_start("测试任务")
        logger_manager.log_step("测试步骤", "测试详情")
        logger_manager.log_task_end("测试任务", True)
        print("✓ 任务日志功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 日志测试失败: {e}")
        return False

def test_agents():
    """测试智能体"""
    print("\n测试智能体...")
    
    try:
        from agents.bubble_sort_agent import bubble_sort_agent
        from agents.html_agent import html_agent
        
        # 测试冒泡排序智能体
        bubble_info = bubble_sort_agent.get_current_step_info()
        print(f"✓ 冒泡排序智能体状态: {bubble_info['step_name']}")
        
        # 测试HTML智能体
        html_info = html_agent.get_current_step_info()
        print(f"✓ HTML智能体状态: {html_info['step_name']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 智能体测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("智能体全流程展示界面 - 项目设置测试")
    print("=" * 60)
    
    tests = [
        test_directories,
        test_imports,
        test_config,
        test_logger,
        test_agents
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！项目设置正确。")
        print("\n可以运行以下命令启动应用:")
        print("python run.py")
    else:
        print("✗ 部分测试失败，请检查项目设置。")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
