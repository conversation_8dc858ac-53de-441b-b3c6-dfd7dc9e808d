/* 智能体展示界面样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 标题区域 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 配置区域 */
.config-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.config-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.config-item {
    display: flex;
    flex-direction: column;
}

.config-item label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

.config-item input,
.config-item select {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.config-item input:focus,
.config-item select:focus {
    outline: none;
    border-color: #3498db;
}

/* 功能区域 */
.function-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.function-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 10px;
}

.task-group {
    margin-bottom: 25px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #fafafa;
}

.task-group h3 {
    color: #34495e;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.step-info {
    margin-top: 10px;
    padding: 10px;
    background-color: #e8f4f8;
    border-radius: 5px;
    font-weight: bold;
    color: #2980b9;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-2px);
}

.btn-test {
    background-color: #f39c12;
    color: white;
}

.btn-test:hover {
    background-color: #e67e22;
    transform: translateY(-2px);
}

.btn-step {
    background-color: #27ae60;
    color: white;
}

.btn-step:hover {
    background-color: #229954;
    transform: translateY(-2px);
}

.btn-disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
    cursor: not-allowed;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

/* 展示区域 */
.display-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.display-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #27ae60;
    padding-bottom: 10px;
}

.display-content {
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    overflow: hidden;
}

.display-header {
    background-color: #34495e;
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.display-area {
    padding: 20px;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    background-color: #fafafa;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.welcome-message {
    color: #7f8c8d;
    font-style: italic;
    text-align: center;
    margin-bottom: 10px;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 5px;
    }
    
    .display-header {
        flex-direction: column;
        gap: 10px;
    }
}
