import sys
sys.stdout.flush()

def bubble_sort(arr):
    """
    冒泡排序算法实现
    
    参数:
        arr: 待排序的列表
    
    返回:
        排序后的列表
    
    算法思路:
        通过重复遍历数组，比较相邻元素并交换位置，
        使较大的元素逐渐"冒泡"到数组末尾
    """
    # 获取数组长度
    n = len(arr)
    
    # 如果数组长度小于等于1，直接返回
    if n <= 1:
        return arr
    
    # 创建数组副本，避免修改原数组
    sorted_arr = arr.copy()
    
    # 外层循环控制排序轮数
    # 需要进行 n-1 轮排序
    for i in range(n - 1):
        # 标记本轮是否发生交换
        swapped = False
        
        # 内层循环进行相邻元素比较
        # 每轮后最大的元素会"冒泡"到末尾
        # 所以内层循环的范围逐渐减小
        for j in range(0, n - i - 1):
            # 比较相邻两个元素
            if sorted_arr[j] > sorted_arr[j + 1]:
                # 如果前一个元素大于后一个元素，则交换
                sorted_arr[j], sorted_arr[j + 1] = sorted_arr[j + 1], sorted_arr[j]
                swapped = True
        
        # 如果本轮没有发生任何交换，说明数组已经有序，可以提前结束
        if not swapped:
            break
    
    return sorted_arr


def bubble_sort_with_steps(arr):
    """
    带步骤显示的冒泡排序（用于演示过程）
    
    参数:
        arr: 待排序的列表
    
    返回:
        排序后的列表
    """
    print(f"原始数组: {arr}")
    n = len(arr)
    
    if n <= 1:
        return arr
    
    sorted_arr = arr.copy()
    
    for i in range(n - 1):
        print(f"\n第 {i + 1} 轮排序:")
        swapped = False
        
        for j in range(0, n - i - 1):
            print(f"  比较 {sorted_arr[j]} 和 {sorted_arr[j + 1]}", end="")
            
            if sorted_arr[j] > sorted_arr[j + 1]:
                sorted_arr[j], sorted_arr[j + 1] = sorted_arr[j + 1], sorted_arr[j]
                swapped = True
                print(f" -> 交换: {sorted_arr}")
            else:
                print(f" -> 不交换: {sorted_arr}")
        
        print(f"第 {i + 1} 轮结果: {sorted_arr}")
        
        if not swapped:
            print("本轮无交换发生，排序完成!")
            break
    
    return sorted_arr


def main():
    """
    主函数：包含测试用例和演示
    """
    print("=" * 50, flush=True)
    print("冒泡排序算法演示")
    print("=" * 50)
    
    # 测试用例1：一般情况
    test_case1 = [64, 34, 25, 12, 22, 11, 90]
    print("\n测试用例1：一般情况")
    result1 = bubble_sort(test_case1)
    print(f"原数组: {test_case1}")
    print(f"排序后: {result1}")
    
    # 测试用例2：已排序数组
    test_case2 = [1, 2, 3, 4, 5]
    print("\n测试用例2：已排序数组")
    result2 = bubble_sort(test_case2)
    print(f"原数组: {test_case2}")
    print(f"排序后: {result2}")
    
    # 测试用例3：逆序数组
    test_case3 = [5, 4, 3, 2, 1]
    print("\n测试用例3：逆序数组")
    result3 = bubble_sort(test_case3)
    print(f"原数组: {test_case3}")
    print(f"排序后: {result3}")
    
    # 测试用例4：包含重复元素
    test_case4 = [3, 1, 4, 1, 5, 9, 2, 6, 5]
    print("\n测试用例4：包含重复元素")
    result4 = bubble_sort(test_case4)
    print(f"原数组: {test_case4}")
    print(f"排序后: {result4}")
    
    # 测试用例5：单个元素
    test_case5 = [42]
    print("\n测试用例5：单个元素")
    result5 = bubble_sort(test_case5)
    print(f"原数组: {test_case5}")
    print(f"排序后: {result5}")
    
    # 测试用例6：空数组
    test_case6 = []
    print("\n测试用例6：空数组")
    result6 = bubble_sort(test_case6)
    print(f"原数组: {test_case6}")
    print(f"排序后: {result6}")
    
    # 演示排序过程
    print("\n" + "=" * 50)
    print("详细排序过程演示")
    print("=" * 50)
    demo_array = [64, 34, 25, 12, 22]
    bubble_sort_with_steps(demo_array)
    
    # 性能测试
    print("\n" + "=" * 50)
    print("性能测试")
    print("=" * 50)
    
    import time
    import random
    
    # 生成随机数组进行性能测试
    large_array = [random.randint(1, 1000) for _ in range(100)]
    
    start_time = time.time()
    sorted_large = bubble_sort(large_array)
    end_time = time.time()
    
    print(f"排序100个随机数耗时: {end_time - start_time:.6f} 秒")
    print(f"排序前10个元素: {large_array[:10]}")
    print(f"排序后10个元素: {sorted_large[:10]}")


# 运行主函数
if __name__ == "__main__":

    try:
        main()
    except Exception as e:
        print(f"排序出错: {e}", flush=True)