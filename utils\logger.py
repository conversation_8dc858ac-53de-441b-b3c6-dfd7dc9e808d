"""
日志管理模块
支持按天滚动，保留365天的日志记录
"""
import logging
import os
from datetime import datetime, timedelta
from logging.handlers import TimedRotatingFileHandler
import yaml
import glob


class LoggerManager:
    def __init__(self, config_path="configs/config.yaml"):
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self._cleanup_old_logs()
    
    def _load_config(self, config_path):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            # 如果配置文件不存在，使用默认配置
            return {
                'logging': {
                    'level': 'INFO',
                    'retention_days': 365,
                    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                }
            }
    
    def _setup_logger(self):
        """设置日志记录器"""
        # 确保logs目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 创建logger
        logger = logging.getLogger('AgentDemo')
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 清除已有的处理器
        logger.handlers.clear()
        
        # 创建按天滚动的文件处理器
        log_filename = os.path.join('logs', 'agent_demo.log')
        file_handler = TimedRotatingFileHandler(
            log_filename,
            when='midnight',
            interval=1,
            backupCount=self.config['logging']['retention_days'],
            encoding='utf-8'
        )
        
        # 设置日志格式
        formatter = logging.Formatter(self.config['logging']['log_format'])
        file_handler.setFormatter(formatter)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _cleanup_old_logs(self):
        """清理超过保留期的日志文件"""
        try:
            retention_days = self.config['logging']['retention_days']
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            log_pattern = os.path.join('logs', 'agent_demo.log.*')
            for log_file in glob.glob(log_pattern):
                try:
                    file_date = datetime.fromtimestamp(os.path.getctime(log_file))
                    if file_date < cutoff_date:
                        os.remove(log_file)
                        self.logger.info(f"删除过期日志文件: {log_file}")
                except Exception as e:
                    self.logger.warning(f"删除日志文件失败 {log_file}: {e}")
        except Exception as e:
            self.logger.error(f"清理日志文件时出错: {e}")
    
    def get_logger(self):
        """获取logger实例"""
        return self.logger
    
    def log_task_start(self, task_name):
        """记录任务开始"""
        self.logger.info(f"=== 任务开始: {task_name} ===")
    
    def log_task_end(self, task_name, success=True):
        """记录任务结束"""
        status = "成功" if success else "失败"
        self.logger.info(f"=== 任务结束: {task_name} - {status} ===")
    
    def log_step(self, step_name, details=""):
        """记录步骤执行"""
        self.logger.info(f"步骤: {step_name} | {details}")
    
    def log_llm_call(self, prompt, response, model=""):
        """记录大模型调用"""
        self.logger.info(f"LLM调用 [{model}]")
        self.logger.info(f"提示: {prompt[:200]}...")
        self.logger.info(f"响应: {response[:200]}...")
    
    def log_tool_call(self, tool_name, command, result=""):
        """记录工具调用"""
        self.logger.info(f"工具调用: {tool_name}")
        self.logger.info(f"命令: {command}")
        if result:
            self.logger.info(f"结果: {result[:200]}...")


# 全局logger实例
logger_manager = LoggerManager()
logger = logger_manager.get_logger()
