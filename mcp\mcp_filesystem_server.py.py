# 示例：一个简单的文件系统 MCP 服务器 (filesystem_server.py)
from mcp.server.fastmcp import FastMCP
import os
from pathlib import Path

# 创建 MCP 服务器实例
mcp = FastMCP("Filesystem Server", host="localhost", port=3000)

@mcp.tool(description="读取文件内容")
def read_file(filepath: str) -> str:
    """读取并返回文件内容"""
    with open(filepath, "r") as f:
        return f.read()

@mcp.tool(description="列出目录内容")
def list_directory(dirpath: str) -> list:
    """列出给定目录中的所有文件和目录"""
    return os.listdir(dirpath)

@mcp.tool(description="获取当前工作目录")
def get_cwd() -> str:
    """返回当前工作目录"""
    return str(Path.cwd())

if __name__ == "__main__":
    mcp.run()