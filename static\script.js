// 智能体展示界面JavaScript

$(document).ready(function() {
    // 初始化
    loadConfig();
    
    // 配置相关事件
    $('#save_config').click(saveConfig);
    $('#load_config').click(loadConfig);
    
    // 大模型测试
    $('#test_llm').click(testLLM);
    
    // 冒泡排序任务事件
    $('#bubble_reset').click(() => resetTask('bubble_sort'));
    $('#bubble_step1').click(() => executeStep('bubble_sort', 1));
    $('#bubble_step2').click(() => executeStep('bubble_sort', 2));
    $('#bubble_step3').click(() => executeStep('bubble_sort', 3));
    $('#bubble_step4').click(() => executeStep('bubble_sort', 4));
    $('#bubble_step5').click(() => executeStep('bubble_sort', 5));
    $('#bubble_step6').click(() => executeStep('bubble_sort', 6));
    $('#bubble_step7').click(() => executeStep('bubble_sort', 7));
    
    // HTML任务事件
    $('#html_reset').click(() => resetTask('html'));
    $('#html_step1').click(() => executeStep('html', 1));
    $('#html_step2').click(() => executeStep('html', 2));
    $('#html_step3').click(() => executeStep('html', 3));
    $('#html_step4').click(() => executeStep('html', 4));
    $('#html_step5').click(() => executeStep('html', 5));
    $('#html_step6').click(() => executeStep('html', 6));
    $('#html_step7').click(() => executeStep('html', 7));
    
    // 清空显示
    $('#clear_display').click(clearDisplay);
    
    // 初始化步骤状态
    updateStepStatus('bubble_sort');
    updateStepStatus('html');
});

// 保存配置
function saveConfig() {
    const config = {
        base_url: $('#base_url').val(),
        api_key: $('#api_key').val(),
        model: $('#model').val(),
        temperature: parseFloat($('#temperature').val()),
        max_tokens: parseInt($('#max_tokens').val())
    };
    
    showLoading();
    
    $.ajax({
        url: '/api/config/save',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(config),
        success: function(response) {
            hideLoading();
            if (response.success) {
                appendToDisplay('配置保存', '配置已成功保存到文件', 'success');
            } else {
                appendToDisplay('配置保存', '保存失败: ' + response.error, 'error');
            }
        },
        error: function() {
            hideLoading();
            appendToDisplay('配置保存', '保存配置时发生错误', 'error');
        }
    });
}

// 加载配置
function loadConfig() {
    $.ajax({
        url: '/api/config/load',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const config = response.config;
                $('#base_url').val(config.base_url || '');
                $('#api_key').val(config.api_key || '');
                $('#model').val(config.model || 'gpt-3.5-turbo');
                $('#temperature').val(config.temperature || 0.7);
                $('#max_tokens').val(config.max_tokens || 2000);
                appendToDisplay('配置加载', '配置已从文件加载', 'success');
            } else {
                appendToDisplay('配置加载', '加载失败: ' + response.error, 'error');
            }
        },
        error: function() {
            appendToDisplay('配置加载', '加载配置时发生错误', 'error');
        }
    });
}

// 测试大模型连接
function testLLM() {
    showLoading();
    $('#current_task').text('当前任务: 大模型连接测试');
    
    $.ajax({
        url: '/api/llm/test',
        method: 'POST',
        success: function(response) {
            hideLoading();
            const status = response.success ? 'success' : 'error';
            appendToDisplay('大模型连接测试', response.message, status);
        },
        error: function() {
            hideLoading();
            appendToDisplay('大模型连接测试', '测试请求失败', 'error');
        }
    });
}

// 重置任务
function resetTask(taskType) {
    showLoading();
    
    $.ajax({
        url: `/api/${taskType}/reset`,
        method: 'POST',
        success: function(response) {
            hideLoading();
            if (response.success) {
                appendToDisplay(`${getTaskName(taskType)}重置`, '任务已重置', 'success');
                updateStepStatus(taskType);
            } else {
                appendToDisplay(`${getTaskName(taskType)}重置`, '重置失败: ' + response.error, 'error');
            }
        },
        error: function() {
            hideLoading();
            appendToDisplay(`${getTaskName(taskType)}重置`, '重置请求失败', 'error');
        }
    });
}

// 执行步骤
function executeStep(taskType, stepNumber) {
    showLoading();
    $('#current_task').text(`当前任务: ${getTaskName(taskType)} - 步骤${stepNumber}`);
    
    $.ajax({
        url: `/api/${taskType}/step/${stepNumber}`,
        method: 'POST',
        success: function(response) {
            hideLoading();
            const status = response.success ? 'success' : 'error';
            const stepName = response.step_name || `步骤${stepNumber}`;
            
            appendToDisplay(`${getTaskName(taskType)} - ${stepName}`, response.result, status);
            
            if (response.success) {
                updateStepStatus(taskType);
            }
        },
        error: function() {
            hideLoading();
            appendToDisplay(`${getTaskName(taskType)} - 步骤${stepNumber}`, '执行请求失败', 'error');
        }
    });
}

// 更新步骤状态
function updateStepStatus(taskType) {
    $.ajax({
        url: `/api/${taskType}/status`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const status = response.status;
                const statusText = `当前步骤: ${status.step_number}/${status.total_steps} - ${status.step_name}`;
                $(`#${taskType}_current_step`).text(statusText);
            }
        }
    });
}

// 获取任务名称
function getTaskName(taskType) {
    const names = {
        'bubble_sort': '冒泡排序任务',
        'html': 'HTML制作任务'
    };
    return names[taskType] || taskType;
}

// 添加内容到显示区
function appendToDisplay(title, content, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const typeClass = type === 'success' ? 'text-success' : 
                     type === 'error' ? 'text-error' : 'text-info';
    
    const html = `
        <div class="display-item ${typeClass}">
            <div class="display-title">
                <strong>[${timestamp}] ${title}</strong>
            </div>
            <div class="display-content">
                <pre>${content}</pre>
            </div>
        </div>
        <hr>
    `;
    
    $('#display_area').append(html);
    
    // 自动滚动到底部
    const displayArea = $('#display_area')[0];
    displayArea.scrollTop = displayArea.scrollHeight;
}

// 清空显示区
function clearDisplay() {
    $('#display_area').html(`
        <p class="welcome-message">欢迎使用智能体全流程展示界面！</p>
        <p>请选择上方的功能按钮开始操作。</p>
    `);
    $('#current_task').text('当前任务: 无');
}

// 显示加载动画
function showLoading() {
    $('#loading').removeClass('hidden');
}

// 隐藏加载动画
function hideLoading() {
    $('#loading').addClass('hidden');
}

// 添加样式类
$('<style>').text(`
    .text-success { color: #27ae60; }
    .text-error { color: #e74c3c; }
    .text-info { color: #3498db; }
    .display-item { margin-bottom: 15px; }
    .display-title { font-weight: bold; margin-bottom: 5px; }
    .display-content pre { 
        white-space: pre-wrap; 
        word-wrap: break-word; 
        background-color: #f8f9fa; 
        padding: 10px; 
        border-radius: 5px; 
        border-left: 4px solid #3498db;
        margin: 0;
    }
    hr { 
        border: none; 
        border-top: 1px solid #eee; 
        margin: 10px 0; 
    }
`).appendTo('head');
