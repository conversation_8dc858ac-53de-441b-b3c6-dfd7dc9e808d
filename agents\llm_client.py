"""
大模型客户端模块
负责与大模型API进行交互
"""
import requests
import json
from utils.config_manager import config_manager
from utils.logger import logger


class LLMClient:
    def __init__(self):
        self.config = config_manager.get_llm_config()
    
    def update_config(self):
        """更新配置"""
        self.config = config_manager.get_llm_config()
        logger.info("LLM客户端配置已更新")
    
    def test_connection(self):
        """测试大模型连接"""
        try:
            logger.info("开始测试大模型连接...")
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config['model'],
                "messages": [
                    {"role": "user", "content": "请简单介绍一下什么是MCP（Model Context Protocol）？"}
                ],
                "max_tokens": 200,
                "temperature": 0.1
            }
            
            # 发送请求
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                message = result['choices'][0]['message']['content']
                logger.info(f"连接测试成功，响应: {message}")
                return True, f"连接成功！模型响应: {message}"
            else:
                error_msg = f"连接失败，状态码: {response.status_code}, 错误: {response.text}"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"连接测试异常: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def call_llm(self, prompt, system_prompt="", temperature=None, max_tokens=None):
        """调用大模型"""
        try:
            logger.info(f"调用大模型，提示长度: {len(prompt)}")
            
            # 使用配置中的参数，如果没有传入特定值
            temp = temperature if temperature is not None else self.config['temperature']
            max_tok = max_tokens if max_tokens is not None else self.config['max_tokens']
            
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config['model'],
                "messages": messages,
                "temperature": temp,
                "max_tokens": max_tok
            }
            
            # 记录请求详情
            from utils.logger import logger_manager
            logger_manager.log_llm_call(prompt, "", self.config['model'])
            
            # 发送请求
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                message = result['choices'][0]['message']['content']
                print(f"message111111: {result}")
                
                # 记录响应
                logger_manager.log_llm_call(prompt, message, self.config['model'])
                logger.info("大模型调用成功")
                
                return True, message
            else:
                error_msg = f"大模型调用失败，状态码: {response.status_code}, 错误: {response.text}"
                logger.error(error_msg)
                return False, error_msg
                
        except Exception as e:
            error_msg = f"大模型调用异常: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def call_llm_tools(self, prompt, system_prompt="", temperature=None, max_tokens=None):
        """调用大模型"""
        try:
            logger.info(f"调用大模型，提示长度: {len(prompt)}")

            # 使用配置中的参数，如果没有传入特定值
            temp = temperature if temperature is not None else self.config['temperature']
            max_tok = max_tokens if max_tokens is not None else self.config['max_tokens']

            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "PythonExecute",  # 工具名称（必须与模型识别的命令一致）
                        "description": "执行指定路径的Python脚本并捕获输出结果",  # 功能描述（引导模型何时调用）
                        "parameters": {  # 参数规范（JSON Schema格式）
                            "type": "object",
                            "properties": {
                                "script_path": {
                                    "type": "string",
                                    "description": "待执行Python脚本的绝对路径，例如：/home/<USER>/script.py"
                                },
                                "arguments": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "传递给脚本的命令行参数列表，例如：['arg1', 'arg2']",
                                    "default": []  # 可选参数
                                }
                            },
                            "required": ["script_path"]  # 必填参数
                        }
                    }
                }
            ]

            data = {
                "model": self.config['model'],
                "messages": messages,
                "temperature": temp,
                "max_tokens": max_tok,
                "tools": tools,
                "tool_choice": {"type": "function", "function": {"name": "PythonExecute"}}
            }

            # 记录请求详情
            from utils.logger import logger_manager
            logger_manager.log_llm_call(prompt, "", self.config['model'])

            # 发送请求
            response = requests.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                message = result['choices'][0]['message']['content']
                # message = result
                print(f"message111111: {result}")

                tool_call = result["choices"][0]["message"]["tool_calls"][0]
                function_name = tool_call["function"]["name"]
                arguments = json.loads(tool_call["function"]["arguments"])

                # 记录响应
                # logger_manager.log_llm_call(prompt, arguments, self.config['model'])
                logger.info("大模型调用成功")

                return True, arguments
            else:
                error_msg = f"大模型调用失败，状态码: {response.status_code}, 错误: {response.text}"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"大模型调用异常: {str(e)}"
            logger.error(error_msg)
            return False, error_msg


# 全局LLM客户端实例
llm_client = LLMClient()
