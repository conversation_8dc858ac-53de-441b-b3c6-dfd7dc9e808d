"""
智能体全流程展示界面 - Flask Web应用
"""
from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import config_manager
from utils.logger import logger, logger_manager
from agents.llm_client import llm_client
from agents.bubble_sort_agent import bubble_sort_agent
from agents.html_agent import html_agent

app = Flask(__name__)

# 确保必要的目录存在
os.makedirs('logs', exist_ok=True)
os.makedirs('output', exist_ok=True)
os.makedirs('static', exist_ok=True)
os.makedirs('templates', exist_ok=True)

@app.route('/')
def index():
    """主页"""
    logger.info("访问主页")
    return render_template('index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

# 配置管理API
@app.route('/api/config/save', methods=['POST'])
def save_config():
    """保存配置"""
    try:
        data = request.get_json()
        logger.info(f"保存配置: {data}")
        
        # 更新配置
        config_manager.update_llm_config(
            base_url=data.get('base_url'),
            api_key=data.get('api_key'),
            model=data.get('model'),
            temperature=data.get('temperature'),
            max_tokens=data.get('max_tokens')
        )
        
        # 更新LLM客户端配置
        llm_client.update_config()
        
        logger.info("配置保存成功")
        return jsonify({'success': True, 'message': '配置保存成功'})
        
    except Exception as e:
        error_msg = f"保存配置失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

@app.route('/api/config/load', methods=['GET'])
def load_config():
    """加载配置"""
    try:
        config = config_manager.get_llm_config()
        logger.info("配置加载成功")
        return jsonify({'success': True, 'config': config})
        
    except Exception as e:
        error_msg = f"加载配置失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

# 大模型测试API
@app.route('/api/llm/test', methods=['POST'])
def test_llm():
    """测试大模型连接"""
    try:
        logger_manager.log_task_start("大模型连接测试")
        success, message = llm_client.test_connection()
        logger_manager.log_task_end("大模型连接测试", success)
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        error_msg = f"测试大模型连接失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'message': error_msg})

# 冒泡排序任务API
@app.route('/api/bubble_sort/reset', methods=['POST'])
def reset_bubble_sort():
    """重置冒泡排序任务"""
    try:
        bubble_sort_agent.reset_task()
        return jsonify({'success': True, 'message': '冒泡排序任务已重置'})
        
    except Exception as e:
        error_msg = f"重置冒泡排序任务失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

@app.route('/api/bubble_sort/step/<int:step_number>', methods=['POST'])
def execute_bubble_sort_step(step_number):
    """执行冒泡排序任务步骤"""
    try:
        logger.info(f"执行冒泡排序任务步骤 {step_number}")
        
        # 检查步骤是否有效
        if step_number < 1 or step_number > len(bubble_sort_agent.steps):
            return jsonify({'success': False, 'error': '无效的步骤编号'})
        
        # 检查是否按顺序执行
        if step_number != bubble_sort_agent.current_step + 1:
            return jsonify({
                'success': False, 
                'error': f'请按顺序执行步骤，当前应执行步骤 {bubble_sort_agent.current_step + 1}'
            })
        
        # 执行当前步骤
        success, result = bubble_sort_agent.execute_current_step()
        
        step_name = bubble_sort_agent.steps[step_number - 1] if step_number <= len(bubble_sort_agent.steps) else f"步骤{step_number}"
        
        return jsonify({
            'success': success,
            'result': result,
            'step_name': step_name,
            'step_number': step_number
        })
        
    except Exception as e:
        error_msg = f"执行冒泡排序步骤失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

@app.route('/api/bubble_sort/status', methods=['GET'])
def get_bubble_sort_status():
    """获取冒泡排序任务状态"""
    try:
        status = bubble_sort_agent.get_current_step_info()
        return jsonify({'success': True, 'status': status})
        
    except Exception as e:
        error_msg = f"获取冒泡排序状态失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

# HTML任务API
@app.route('/api/html/reset', methods=['POST'])
def reset_html():
    """重置HTML任务"""
    try:
        html_agent.reset_task()
        return jsonify({'success': True, 'message': 'HTML任务已重置'})
        
    except Exception as e:
        error_msg = f"重置HTML任务失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

@app.route('/api/html/step/<int:step_number>', methods=['POST'])
def execute_html_step(step_number):
    """执行HTML任务步骤"""
    try:
        logger.info(f"执行HTML任务步骤 {step_number}")
        
        # 检查步骤是否有效
        if step_number < 1 or step_number > len(html_agent.steps):
            return jsonify({'success': False, 'error': '无效的步骤编号'})
        
        # 检查是否按顺序执行
        if step_number != html_agent.current_step + 1:
            return jsonify({
                'success': False, 
                'error': f'请按顺序执行步骤，当前应执行步骤 {html_agent.current_step + 1}'
            })
        
        # 执行当前步骤
        success, result = html_agent.execute_current_step()
        
        step_name = html_agent.steps[step_number - 1] if step_number <= len(html_agent.steps) else f"步骤{step_number}"
        
        return jsonify({
            'success': success,
            'result': result,
            'step_name': step_name,
            'step_number': step_number
        })
        
    except Exception as e:
        error_msg = f"执行HTML步骤失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

@app.route('/api/html/status', methods=['GET'])
def get_html_status():
    """获取HTML任务状态"""
    try:
        status = html_agent.get_current_step_info()
        return jsonify({'success': True, 'status': status})
        
    except Exception as e:
        error_msg = f"获取HTML状态失败: {str(e)}"
        logger.error(error_msg)
        return jsonify({'success': False, 'error': error_msg})

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"内部服务器错误: {str(error)}")
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    logger.info("启动智能体展示界面服务")
    print("智能体全流程展示界面启动中...")
    print("访问地址: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
