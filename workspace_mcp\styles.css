```css
/* CSS变量定义 */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --text-color: #333;
    --text-light: #666;
    --bg-color: #f8f9fa;
    --bg-white: #ffffff;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --font-main: 'Microsoft YaHei', Arial, sans-serif;
    --font-secondary: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-main);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    font-size: 16px;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 标题样式 */
h1, h2, h3, h4 {
    font-family: var(--font-secondary);
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1.3;
}

h1 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 2rem;
}

h2 {
    font-size: 1.8rem;
    position: relative;
    padding-bottom: 0.5rem;
    margin-top: 2rem;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--secondary-color);
    border-radius: 2px;
}

h3 {
    font-size: 1.4rem;
    color: var(--secondary-color);
}

/* 段落样式 */
p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* 链接样式 */
a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

/* 列表样式 */
ul, ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

li {
    margin-bottom: 0.5rem;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: var(--bg-white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
    text-align: center;
}

.btn:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn-accent {
    background-color: var(--accent-color);
}

.btn-accent:hover {
    background-color: #c0392b;
}

/* 卡片样式 */
.card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

/* 头部区域 */
.header {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--primary-color), #1a2530);
    color: white;
    margin-bottom: 2rem;
}

.header h1 {
    color: white;
    margin-bottom: 0.5rem;
}

.header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
}

/* 导航栏 */
.navbar {
    background-color: var(--bg-white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-list {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0 1rem;
}

.nav-link {
    padding: 1rem 0;
    display: block;
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--secondary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--secondary-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

/* 内容区域 */
.content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    margin-bottom: 3rem;
}

/* 左侧内容 */
.main-content {
    padding-right: 2rem;
}

/* 右侧边栏 */
.sidebar {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

/* 技能条样式 */
.skill-bar {
    margin-bottom: 1.5rem;
}

.skill-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.skill-progress {
    height: 10px;
    background-color: #ecf0f1;
    border-radius: 5px;
    overflow: hidden;
}

.skill-progress-inner {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    border-radius: 5px;
    transition: width 1s ease-in-out;
}

/* 工作经验样式 */
.work-item {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.work-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.work-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.work-title {
    font-size: 1.3rem;
    margin-bottom: 0;
}

.work-company {
    color: var(--secondary-color);
    font-weight: 600;
}

/* 教育背景样式 */
.education-item {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.education-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.5rem;
    width: 10px;
    height: 10px;
    background: var(--secondary-color);
    border-radius: 50%;
}

/* 联系方式 */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
}

.contact-icon {
    margin-right: 0.5rem;
    color: var(--secondary-color);
    font-size: 1.2rem;
}

/* 社交媒体链接 */
.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 4